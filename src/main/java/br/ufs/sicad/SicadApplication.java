package br.ufs.sicad;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.web.config.EnableSpringDataWebSupport;

/**
 * Classe principal da aplicação SICAD (Sistema de gestão e acompanhamento de Contratos e Processos).
 *
 * <p>O SICAD é um sistema web desenvolvido para gerenciar contratos institucionais, notas fiscais,
 * requisições internas, autorizações de acesso administrativo e documentos operacionais,
 * com foco em centralização, padronização e rastreabilidade das informações envolvidas
 * nesses processos.</p>
 *
 * <p>Esta classe contém o método main que serve como ponto de entrada da aplicação Spring Boot.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 */
@EnableSpringDataWebSupport(pageSerializationMode = EnableSpringDataWebSupport.PageSerializationMode.VIA_DTO)
@SpringBootApplication
public class SicadApplication {

	/**
	 * Método principal que inicia a aplicação Spring Boot.
	 *
	 * <p>Este método configura e executa o contexto da aplicação Spring Boot,
	 * inicializando todos os componentes necessários para o funcionamento do sistema SICAD.</p>
	 *
	 * @param args argumentos da linha de comando passados para a aplicação
	 */
	public static void main(String[] args) {
		SpringApplication.run(SicadApplication.class, args);
		System.out.println("Em execução");
	}

}
