package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.ContratoDTO;
import br.ufs.sicad.api.forms.ContratoDTOForm;
import br.ufs.sicad.api.forms.ContratoDTOUpdateForm;
import br.ufs.sicad.api.dtos.ContratoDetalhadoDTO;
import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.services.ContratoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller REST responsável pelo gerenciamento de contratos no sistema SICAD.
 *
 * <p>Este controller fornece endpoints para operações CRUD (Create, Read, Update, Delete)
 * de contratos, incluindo funcionalidades para:</p>
 * <ul>
 *   <li>Criação de novos contratos</li>
 *   <li>Listagem de contratos com filtros</li>
 *   <li>Busca de contratos por ID</li>
 *   <li>Atualização de contratos existentes</li>
 *   <li>Associação de contratos a processos</li>
 * </ul>
 *
 * <p>Todos os endpoints retornam dados no formato JSON e seguem os padrões REST.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/contratos")
@RequiredArgsConstructor
public class ContratoController {

    private final ContratoService contratoService;

    /**
     * Cria um novo contrato no sistema.
     *
     * <p>Este endpoint permite a criação de um novo contrato associando-o aos fornecedores
     * e itens especificados no formulário de entrada.</p>
     *
     * @param contratoForm formulário contendo os dados do contrato a ser criado
     * @return ResponseEntity contendo o DTO do contrato criado e status HTTP 201 (CREATED)
     */
    @PostMapping
    public ResponseEntity<ContratoDetalhadoDTO> criarContrato(@RequestBody @Valid ContratoDTOForm contratoForm) {
        Contrato contrato = contratoService.criarContrato(
                contratoForm.asContrato(),
                contratoForm.fornecedorIds(),
                contratoForm.itemIds(),
                contratoForm.idProcesso(),
                contratoForm.contratoId()
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(ContratoDetalhadoDTO.from(contrato));
    }

    /**
     * Lista contratos com filtros opcionais e paginação.
     *
     * <p>Este endpoint permite buscar contratos aplicando diversos filtros como número do contrato,
     * status, período de datas, fornecedores e itens. Os resultados são paginados.</p>
     *
     * @param numContrato filtro opcional pelo número do contrato
     * @param status filtro opcional pelo status do contrato
     * @param dataInicial filtro opcional pela data inicial do período
     * @param dataFinal filtro opcional pela data final do período
     * @param fornecedorId filtro opcional pela lista de IDs dos fornecedores
     * @param page número da página (padrão: 0)
     * @param size tamanho da página (padrão: 30)
     * @return ResponseEntity contendo a página de contratos encontrados
     */
    @GetMapping
    public ResponseEntity<Page<ContratoDTO>> listarContratos(
            @RequestParam(required = false) String numContrato,
            @RequestParam(required = false) String tipo,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) LocalDate dataInicial,
            @RequestParam(required = false) LocalDate dataFinal,
            @RequestParam(required = false) Long fornecedorId,
            @RequestParam(required = false) Long contratoReferencia,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "30") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Contrato> contratos = contratoService.listarContratos(numContrato, tipo, status, dataInicial, dataFinal, fornecedorId, contratoReferencia, pageable);

        List<ContratoDTO> contratosDTO = contratos.getContent().stream()
                .map(ContratoDTO::from)
                .collect(Collectors.toList());

        Page<ContratoDTO> contratosPage = new PageImpl<>(contratosDTO, pageable, contratos.getTotalElements());

        return ResponseEntity.ok(contratosPage);
    }

    /**
     * Busca um contrato específico pelo seu ID.
     *
     * @param id identificador único do contrato
     * @return ResponseEntity contendo o DTO do contrato encontrado
     */
    @GetMapping("/{id}")
    public ResponseEntity<ContratoDetalhadoDTO> buscarContrato(@PathVariable Long id) {
        Contrato contrato = contratoService.buscarContratoPor(id);
        return ResponseEntity.ok(ContratoDetalhadoDTO.from(contrato));
    }

    /**
     * Atualiza um contrato existente.
     *
     * <p>Este endpoint permite atualizar os dados de um contrato existente,
     * incluindo a modificação dos fornecedores e itens associados.</p>
     *
     * @param id identificador único do contrato a ser atualizado
     * @param contratoForm formulário contendo os novos dados do contrato
     * @return ResponseEntity contendo o DTO do contrato atualizado
     */
    @PutMapping("/{id}")
    public ResponseEntity<ContratoDetalhadoDTO> atualizarContrato(
            @PathVariable Long id,
            @RequestBody @Valid ContratoDTOUpdateForm contratoForm) {
        Contrato contrato = contratoService.atualizarContrato(
                id,
                contratoForm.asContrato(),
                contratoForm.processoId(),
                contratoForm.fornecedorIds(),
                contratoForm.itemIds()
        );
        return ResponseEntity.ok(ContratoDetalhadoDTO.from(contrato));
    }
} 