package br.ufs.sicad.api.controllers;

import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import br.ufs.sicad.api.dtos.*;
import br.ufs.sicad.domain.enums.StatusProcesso;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.domain.enums.Status;
import br.ufs.sicad.services.ProcessoService;
import br.ufs.sicad.services.ContratoService;
import br.ufs.sicad.services.UnidadeOrganizacionalService;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final ProcessoService processoService;
    private final ContratoService contratoService;
    private final UnidadeOrganizacionalService unidadeService;

    @GetMapping("quantitativo-processos")
    public ResponseEntity<DashboardQuantitativoDTO> obterQuantitativoProcessos(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String tipo,
            @RequestParam(required = false) String modalidade,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFim) {

        if (dataInicio != null && dataFim != null && dataInicio.isAfter(dataFim)) {
            return ResponseEntity.badRequest().build();
        }

        StatusProcesso statusProcesso = null;
        if (status != null) {
            statusProcesso = StatusProcesso.fromString(status);
            if (statusProcesso == null) {
                return ResponseEntity.badRequest().build();
            }
        }

        Long total = processoService.contarProcessos(status, tipo, modalidade, dataInicio, dataFim);
        DashboardQuantitativoDTO response = DashboardQuantitativoDTO.of(total);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("processos-por-status")
    public ResponseEntity<DashboardProcessosPorStatusDTO> obterProcessosPorStatus(
            @RequestParam(required = false) String tipo,
            @RequestParam(required = false) String modalidade,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFim) {

        if (dataInicio != null && dataFim != null && dataInicio.isAfter(dataFim)) {
            return ResponseEntity.badRequest().build();
        }

        Long[] contadores = processoService.contarProcessosPorStatus(tipo, modalidade, dataInicio, dataFim);
        DashboardProcessosPorStatusDTO response = DashboardProcessosPorStatusDTO.of(
                contadores[0], contadores[1], contadores[2], contadores[3], contadores[4]);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("processos-por-tipo")
    public ResponseEntity<DashboardProcessosPorTipoDTO> obterProcessosPorTipo(
            @RequestParam(required = false) String modalidade,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFim) {

        if (dataInicio != null && dataFim != null && dataInicio.isAfter(dataFim)) {
            return ResponseEntity.badRequest().build();
        }

        List<DashboardProcessosPorTipoDTO.TipoProcessoDTO> tipos = processoService.contarProcessosPorTipo(modalidade, dataInicio, dataFim);
        DashboardProcessosPorTipoDTO response = DashboardProcessosPorTipoDTO.of(tipos);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("contratos-vencendo")
    public ResponseEntity<DashboardContratosVencendoDTO> obterContratosVencendo() {
        List<DashboardContratosVencendoDTO.MesVencimentoDTO> meses = contratoService.contarContratosVencendoPorMes();
        DashboardContratosVencendoDTO response = DashboardContratosVencendoDTO.of(meses);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("quantitativo-contratos")
    public ResponseEntity<DashboardQuantitativoContratosDTO> obterQuantitativoContratos(
            @RequestParam(required = false) String numContrato,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String tipo,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicial,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFinal,
            @RequestParam(required = false) Long fornecedorId) {

        if (dataInicial != null && dataFinal != null && dataInicial.isAfter(dataFinal)) {
            return ResponseEntity.badRequest().build();
        }

        if (status != null && StatusContrato.fromString(status) == null) {
            return ResponseEntity.badRequest().build();
        }

        Long total = contratoService.contarContratos(numContrato, status, tipo, dataInicial, dataFinal, fornecedorId);
        DashboardQuantitativoContratosDTO response = DashboardQuantitativoContratosDTO.of(total);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("quantitativo-aditivos")
    public ResponseEntity<DashboardQuantitativoAditivosDTO> obterQuantitativoAditivos(
            @RequestParam(required = false) String numContrato,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicial,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFinal,
            @RequestParam(required = false) Long fornecedorId) {

        if (dataInicial != null && dataFinal != null && dataInicial.isAfter(dataFinal)) {
            return ResponseEntity.badRequest().build();
        }

        if (status != null && StatusContrato.fromString(status) == null) {
            return ResponseEntity.badRequest().build();
        }

        Long total = contratoService.contarAditivos(numContrato, status, dataInicial, dataFinal, fornecedorId);
        DashboardQuantitativoAditivosDTO response = DashboardQuantitativoAditivosDTO.of(total);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("quantitativo-unidades")
    public ResponseEntity<DashboardQuantitativoUnidadesDTO> obterQuantitativoUnidades(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String sigla,
            @RequestParam(required = false) String status) {

        if (status != null && Status.fromString(status) == null) {
            return ResponseEntity.badRequest().build();
        }

        Long total = unidadeService.contarUnidades(nome, sigla, status);
        DashboardQuantitativoUnidadesDTO response = DashboardQuantitativoUnidadesDTO.of(total);
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
