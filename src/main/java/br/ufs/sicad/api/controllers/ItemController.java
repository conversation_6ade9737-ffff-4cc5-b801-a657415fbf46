package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.ItemDTO;
import br.ufs.sicad.api.forms.ItemForm;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.services.ItemService;
import br.ufs.sicad.services.ProcessoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("itens")
@RequiredArgsConstructor
public class ItemController {

    private final ProcessoService processoService;
    private final ItemService itemService;

     @PostMapping
    public ResponseEntity<ItemDTO> adicionarItem(@Valid @RequestBody ItemForm form) {
        Item novoItem = itemService.criarItem(form.idProcesso(), form.asItem());
        return ResponseEntity.status(HttpStatus.CREATED).body(ItemDTO.from(novoItem));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ItemDTO> buscarItemPorId(@PathVariable Long id) {
        Item item = itemService.buscarPorId(id);
        return ResponseEntity.ok(ItemDTO.from(item));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ItemDTO> atualizarItem(@PathVariable Long id, @Valid @RequestBody ItemForm form) {
        Item itemAtualizado = itemService.atualizarItem(id, form.asItem());
        return ResponseEntity.ok(ItemDTO.from(itemAtualizado));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> cancelarItem(@PathVariable Long id) {
        itemService.cancelarItem(id);
        return ResponseEntity.noContent().build();
    }
}