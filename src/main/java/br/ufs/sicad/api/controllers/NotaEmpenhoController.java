package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.NotaEmpenhoDTO;
import br.ufs.sicad.api.dtos.NotaEmpenhoDetalhadaDTO;
import br.ufs.sicad.api.forms.AnularNotaEmpenhoForm;
import br.ufs.sicad.api.forms.NotaEmpenhoForm;
import br.ufs.sicad.api.forms.NotaEmpenhoItemForm;
import br.ufs.sicad.api.forms.NotaEmpenhoUpdateForm;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.services.NotaEmpenhoService;
import br.ufs.sicad.utils.Utils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notas-empenho")
@RequiredArgsConstructor
@Tag(name = "Notas de Empenho", description = "Endpoints Notas de Empenho")
public class NotaEmpenhoController {

    private final NotaEmpenhoService notaEmpenhoService;

    @GetMapping
    @Operation(summary = "Listar nostas com ou sem filtros.")
    public ResponseEntity<Page<NotaEmpenhoDTO>> listarNotasEmpenho(
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String numProcesso,
            @RequestParam(required = false) String numContrato,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String prazoEntrega,
            @Parameter(hidden = true) Pageable pageable) {
        
        Page<NotaEmpenho> notasEmpenho = notaEmpenhoService.listar(numero, numProcesso, numContrato, Utils.converter(prazoEntrega), status, pageable);
        Page<NotaEmpenhoDTO> dtos = notasEmpenho.map(NotaEmpenhoDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Buscar nota por id.")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> buscarPorId(@PathVariable Long id) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.buscarPorId(id);
        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }


    @PostMapping
    @Operation(summary = "Criar nota de empenho.")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> criar(@RequestBody @Valid NotaEmpenhoForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.criar(form.asNotaEmpenho(),
                form.idFornecedor(),
                form.idContrato(),
                form.itens()
                        .stream()
                        .map(NotaEmpenhoItemForm::asNotaEmpenhoItem)
                        .toList()
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Atualizar nota de empenho por id.")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> atualizar(@PathVariable Long id,
                                                             @RequestBody @Valid NotaEmpenhoUpdateForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.atualizar(
                id,
                form.numero(),
                form.ano(),
                Utils.converter(form.dataEmissao()),
                Utils.converter(form.prazoEntrega()),
                form.idFornecedor()
        );

        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Inativar nota de empenho por id.")
    public ResponseEntity<Void> inativar(@PathVariable Long id) {
        notaEmpenhoService.inativar(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/reativar")
    @Operation(summary = "Reativar nota de empenho por id.")
    public ResponseEntity<Void> reativar(@PathVariable Long id) {
        notaEmpenhoService.reativar(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/reduzir-itens")
    @Operation(summary = "Reduz a quantidade de itens da nota de empenho por id.")
    public ResponseEntity<Void> anularEmpenhoParcial(@PathVariable Long id,
                                                     @RequestBody AnularNotaEmpenhoForm anularNotaEmpenhoForm) {
        notaEmpenhoService.anularEmpenhoParcial(id, anularNotaEmpenhoForm.idItem(), anularNotaEmpenhoForm.quantidade());
        return ResponseEntity.ok().build();
    }

}
