package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.PerfilUsuarioDTO;
import br.ufs.sicad.services.PerfilService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("perfis")
public class PerfilController {

    private final PerfilService perfilService;

    public PerfilController(PerfilService perfilService) {
        this.perfilService = perfilService;
    }

    @GetMapping
    public ResponseEntity<List<PerfilUsuarioDTO>> listarPerfis(){
        List<PerfilUsuarioDTO> perfis_usuario = perfilService.listarPerfis()
                .stream()
                .map(PerfilUsuarioDTO::from)
                .toList();
        return ResponseEntity.status(HttpStatus.OK).body(perfis_usuario);
    }


}
