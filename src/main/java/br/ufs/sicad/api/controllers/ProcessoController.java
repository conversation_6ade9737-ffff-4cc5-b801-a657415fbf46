package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.forms.AtualizaProcessoForm;
import br.ufs.sicad.api.forms.ProcessoForm;
import br.ufs.sicad.api.forms.RegistroDistribuicaoForm;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import br.ufs.sicad.api.dtos.*;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.enums.StatusItem;
import br.ufs.sicad.services.ContratoService;
import br.ufs.sicad.services.ItemService;
import br.ufs.sicad.services.ProcessoService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller REST responsável pelo gerenciamento de processos no sistema SICAD.
 *
 * <p>Este controller fornece endpoints para operações CRUD de processos, incluindo:</p>
 * <ul>
 *   <li>Criação de novos processos</li>
 *   <li>Listagem de processos com filtros</li>
 *   <li>Busca de processos por ID</li>
 *   <li>Atualização de processos existentes</li>
 *   <li>Arquivamento e ativação de processos</li>
 * </ul>
 *
 * <p>O controller suporta diferentes tipos de processos como ProcessoSpal e outros.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("processos")
@RequiredArgsConstructor
public class ProcessoController {

    private final ProcessoService processoService;
    private final ItemService itemService;
    private final ContratoService contratoService;

    /**
     * Lista processos com filtros opcionais e paginação.
     *
     * @param numeroSEI filtro opcional pelo número SEI do processo
     * @param tipo filtro opcional pelo tipo do processo
     * @param modalidade filtro opcional pela modalidade do processo
     * @param status filtro opcional pelo status do processo
     * @param page número da página (padrão: 0)
     * @param size tamanho da página (padrão: 10)
     * @return ResponseEntity contendo a página de processos encontrados
     */
    @GetMapping
    public ResponseEntity<Page<ProcessoDTO>> listarProcessos(@RequestParam(required = false) String numeroSEI,
                                                             @RequestParam(required = false) String tipo,
                                                             @RequestParam(required = false) String modalidade,
                                                             @RequestParam(required = false) String status,
                                                             @RequestParam(defaultValue = "0") int page,
                                                             @RequestParam(defaultValue = "10") int size) {



        Pageable pageable = PageRequest.of(page, size);
        Page<Processo> processos = processoService.listarProcessos(numeroSEI, tipo, modalidade, status, pageable);

        List<ProcessoDTO> processosDTO = processos.getContent()
                .stream()
                .map(ProcessoDTO::from)
                .toList();

        Page<ProcessoDTO> processosPage = new PageImpl<>(processosDTO, pageable, processos.getTotalElements());

        return ResponseEntity.status(HttpStatus.OK).body(processosPage);
    }

    /**
     * Busca um processo específico pelo seu ID com informações detalhadas.
     *
     * @param id identificador único do processo
     * @return ResponseEntity contendo o DTO detalhado do processo encontrado
     */
    @GetMapping("{id}")
    public ResponseEntity<ProcessoDetalhadoDTO> buscarProcesso(@PathVariable Long id){
        Processo processo = processoService.buscarProcessoPorId(id);
        return ResponseEntity.status(HttpStatus.OK).body(ProcessoDetalhadoDTO.from(processo));
    }

    /**
     * Cria um novo processo no sistema.
     *
     * <p>Este endpoint permite a criação de um novo processo, incluindo a associação
     * com processos relacionados quando aplicável.</p>
     *
     * @param processoForm formulário contendo os dados do processo a ser criado
     * @return ResponseEntity contendo o DTO do processo criado e status HTTP 201 (CREATED)
     */
    @PostMapping
    public ResponseEntity<ProcessoDTO> criarProcesso(@RequestBody ProcessoForm processoForm) {
        Processo processoSalvo = processoService.criarProcesso(processoForm.asProcesso());
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDTO.from(processoSalvo));
    }

    @PutMapping("{id}")
    public ResponseEntity<ProcessoDTO> atualizarProcesso(@PathVariable Long id, @RequestBody AtualizaProcessoForm processoForm){
        Processo processoAtualizado = processoService.atualizarProcesso(id, processoForm.asProcesso());
        return ResponseEntity.status(HttpStatus.OK).body(ProcessoDTO.from(processoAtualizado));
    }

    @PostMapping("/{id}/distribuicoes")
    public ResponseEntity<ProcessoDTO> adicionarRegistroDistribuicao(
            @PathVariable Long id,
            @RequestBody @Valid RegistroDistribuicaoForm form) {
        Processo processoAtualizado = processoService.adicionarRegistroDistribuicao(id, form);
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDTO.from(processoAtualizado));
    }

    @DeleteMapping("{id}")
    public ResponseEntity<ProcessoDetalhadoDTO> arquivarProcesso(@PathVariable Long id){
        Processo processoArquivado = processoService.arquivarProcesso(id);
        return ResponseEntity.status(HttpStatus.OK).body(ProcessoDetalhadoDTO.from(processoArquivado));
    }

    @PutMapping("{id}/ativar")
    public ResponseEntity<ProcessoDetalhadoDTO> ativarProcesso(@PathVariable Long id){
        Processo processoAtivo = processoService.ativarProcesso(id);
        return ResponseEntity.status(HttpStatus.OK).body(ProcessoDetalhadoDTO.from(processoAtivo));
    }

    @GetMapping("{id}/contratos")
    public ResponseEntity<List<ContratoDTO>> buscarContratosDoProcesso(@PathVariable Long id) {
        List<ContratoDTO> contratos = contratoService.listarContratosPorProcesso(id).stream()
                .map(ContratoDTO::from)
                .toList();
        return ResponseEntity.status(HttpStatus.OK).body(contratos);
    }

    @GetMapping("{id}/itens")
    public ResponseEntity<List<ItemDTO>> listarItensDoProcesso(@PathVariable Long id) {
        List<ItemDTO> itensDTO = itemService.listarItensPorProcesso(id).stream()
                .filter(item -> item.getStatus() != StatusItem.INATIVO)
                .map(ItemDTO::from)
                .collect(Collectors.toList());
        return ResponseEntity.ok(itensDTO);
    }
}