package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.RegistroDistribuicaoDTO;
import br.ufs.sicad.api.forms.RegistroDistribuicaoForm;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.RegistroDistribuicao;
import br.ufs.sicad.services.RegistroDistribuicaoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/itens/{itemId}/distribuicoes")
@RequiredArgsConstructor
public class RegistroDistribuicaoController {

    private final RegistroDistribuicaoService distribuicaoService;
    
    @PostMapping
    public ResponseEntity<RegistroDistribuicaoDTO> registrarDistribuicao(@PathVariable Long itemId,
                                                                         @Valid @RequestBody RegistroDistribuicaoForm form) {
        if (!itemId.equals(form.itemId())) {
            throw new ValidationException("O ID do item na URL (" + itemId + ") não corresponde ao ID no corpo da requisição (" + form.itemId() + ").");
        }

        RegistroDistribuicao novoRegistro = distribuicaoService.criar(form);
        return ResponseEntity.status(HttpStatus.CREATED).body(RegistroDistribuicaoDTO.from(novoRegistro));
    }
}