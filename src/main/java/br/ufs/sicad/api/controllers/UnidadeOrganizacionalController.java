package br.ufs.sicad.api.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import br.ufs.sicad.api.dtos.UnidadeOrganizacionalDTO;
import br.ufs.sicad.api.forms.UnidadeOrganizacionalForm;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.services.UnidadeOrganizacionalService;

@RestController
@RequestMapping("/unidades-organizacionais")
@RequiredArgsConstructor
public class UnidadeOrganizacionalController {

    private final UnidadeOrganizacionalService service;

    @GetMapping
    public ResponseEntity<Page<UnidadeOrganizacionalDTO>> listarTodos(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String sigla,
            @RequestParam(required = false) String status,
            Pageable pageable) {
        
        Page<UnidadeOrganizacional> unidades = service.listarComFiltros(nome, sigla, status, pageable);
        Page<UnidadeOrganizacionalDTO> dtos = unidades.map(UnidadeOrganizacionalDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UnidadeOrganizacionalDTO> buscarPorId(@PathVariable Long id) {
        UnidadeOrganizacional unidade = service.buscarPorId(id);
        return ResponseEntity.ok(UnidadeOrganizacionalDTO.from(unidade));
    }

    @PostMapping
    public ResponseEntity<UnidadeOrganizacionalDTO> criar(@RequestBody @Valid UnidadeOrganizacionalForm unidadeForm) {
        UnidadeOrganizacional unidade = service.criar(unidadeForm.asUnidade(), unidadeForm.unidadeSuperior());
        return ResponseEntity.status(HttpStatus.CREATED).body(UnidadeOrganizacionalDTO.from(unidade));
    }

    @PutMapping("/{id}")
    public ResponseEntity<UnidadeOrganizacionalDTO> atualizar(@PathVariable Long id, @RequestBody @Valid UnidadeOrganizacionalForm unidadeForm) {
        UnidadeOrganizacional unidade = service.atualizar(id, unidadeForm.nome(), unidadeForm.sigla(), unidadeForm.unidadeSuperior());
        return ResponseEntity.ok(UnidadeOrganizacionalDTO.from(unidade));
    }


    @PutMapping("/reativar/{id}")
    public ResponseEntity<Void> ativar(@PathVariable Long id) {
        service.ativar(id);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Void> inativar(@PathVariable Long id) {
        service.inativar(id);
        return ResponseEntity.noContent().build();
    }
}