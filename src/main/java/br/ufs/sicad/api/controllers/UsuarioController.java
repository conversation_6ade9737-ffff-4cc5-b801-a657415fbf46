package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.*;
import br.ufs.sicad.api.forms.AlteraSenhaDtoForm;
import br.ufs.sicad.api.forms.AtualizaUsuarioDTOForm;
import br.ufs.sicad.api.forms.UsuarioDTOForm;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.services.UsuarioService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("usuarios")
public class UsuarioController {

    private final UsuarioService usuarioService;

    public UsuarioController(UsuarioService usuarioService) {
        this.usuarioService = usuarioService;
    }

    @GetMapping
    public ResponseEntity<Page<UsuarioDTO>> listarUsuarios(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String matricula,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long unidadeOrganizacionalId,
            @RequestParam(required = false) Long perfilId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);

        Page<Usuario> usuarios = usuarioService.listarUsuarios(nome, matricula, status,
                unidadeOrganizacionalId, perfilId, pageable);

        List<UsuarioDTO> usuariosDTO = usuarios.getContent()
                .stream()
                .map(UsuarioDTO::from)
                .collect(Collectors.toList());

        Page<UsuarioDTO> usuariosPage = new PageImpl<>(usuariosDTO, pageable, usuarios.getTotalElements());

        return ResponseEntity.status(HttpStatus.OK).body(usuariosPage);
    }

    @GetMapping("/info")
    public ResponseEntity<UsuarioDTO> UsuarioInfo(@AuthenticationPrincipal Jwt jwt) {
        String email = jwt.getSubject();
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioService.buscarUsuarioPor(email)));
    }

    @PostMapping
    public ResponseEntity<UsuarioDTO> criarUsuario(@RequestBody UsuarioDTOForm usuarioDTOForm) {
        Usuario usuarioCriado = usuarioService.criarUsuario(usuarioDTOForm.asUsuario(), usuarioDTOForm.perfilId(), usuarioDTOForm.unidadeId());
        return ResponseEntity.status(HttpStatus.CREATED).body(UsuarioDTO.from(usuarioCriado));
    }

    @GetMapping("{id}")
    public ResponseEntity<UsuarioDTO> buscarUsuario(@PathVariable Long id) {
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioService.buscarUsuarioPor(id)));
    }

    @PutMapping("{id}")
    public ResponseEntity<UsuarioDTO> atualizarUsuario(@PathVariable Long id, @RequestBody AtualizaUsuarioDTOForm atualizaUsuarioDTOForm) {
        Usuario usuarioAtualizado = usuarioService.atualizarUsuario(
                id,
                atualizaUsuarioDTOForm.matricula(),
                atualizaUsuarioDTOForm.nome(),
                atualizaUsuarioDTOForm.sobrenome(),
                atualizaUsuarioDTOForm.email(),
                atualizaUsuarioDTOForm.cpf(),
                atualizaUsuarioDTOForm.telefone(),
                atualizaUsuarioDTOForm.perfilId(),
                atualizaUsuarioDTOForm.unidadeId()
        );
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioAtualizado));
    }

    @PutMapping("/reativar/{id}")
    public ResponseEntity<UsuarioDTO> reativarUsuario(@PathVariable Long id) {
        Usuario usuarioAtivo = usuarioService.reativarUsuairo(id);
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioAtivo));
    }

    @PutMapping("/alterar-senha/{id}")
    public ResponseEntity<String> alterarSenha(@PathVariable Long id, @RequestBody AlteraSenhaDtoForm alteraSenhaDtoForm){
        usuarioService.alterarSenha(id, alteraSenhaDtoForm.senhaAntiga(), alteraSenhaDtoForm.senhaNova());
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body("Senha alterada com sucesso.");
    }

    @PostMapping("/recuperar-senha/{id}")
    public ResponseEntity<String> recuperarSenha(@PathVariable Long id){
        usuarioService.recuperarSenha(id);
        return ResponseEntity.status(HttpStatus.OK).body("Senha redefinida para os 6 primeiros digitos do cpf.");
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Void> inativarUsuario(@PathVariable Long id) {
        usuarioService.inativarUsuario(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
