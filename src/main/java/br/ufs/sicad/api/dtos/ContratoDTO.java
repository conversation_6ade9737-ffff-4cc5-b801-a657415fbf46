package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.utils.Utils;
import java.util.List;

public record ContratoDTO(
        Long id,
        String numero,
        String tipo,
        String objetoContrato,
        String valorTotal,
        StatusContrato status,
        String dataInicial,
        String dataFinal,
        Integer vigencia,
        List<FornecedorDTO> fornecedores

) {
    public static ContratoDTO from(Contrato contrato) {
        return new ContratoDTO(
                contrato.getId(),
                contrato.getNumContrato(),
                contrato.getTipo().name(),
                contrato.getObjetoContrato(),
                contrato.getValorTotal().toString(),
                contrato.getStatus(),
                Utils.converter(contrato.getDataInicial()),
                Utils.converter(contrato.getDataFinal()),
                contrato.getVigencia(),
                contrato.getFornecedores()
                        .stream()
                        .map(FornecedorDTO::from)
                        .toList()
        );
    }
} 