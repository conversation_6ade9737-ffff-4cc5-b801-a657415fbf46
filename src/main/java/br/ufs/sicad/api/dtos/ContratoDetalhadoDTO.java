package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.utils.Utils;

import java.util.List;
import java.util.Objects;

public record ContratoDetalhadoDTO(
        Long id,
        String numero,
        String tipo,
        String objetoContrato,
        String valorTotal,
        StatusContrato status,
        String dataInicial,
        String dataFinal,
        Integer vigencia,
        ProcessoDTO processo,
        Long contratoReferencia,
        List<FornecedorDTO> fornecedores,
        List<ItemDTO> itens
) {
    public static ContratoDetalhadoDTO from(Contrato contrato) {
        return new ContratoDetalhadoDTO(
                contrato.getId(),
                contrato.getNumContrato(),
                contrato.getTipo().name(),
                contrato.getObjetoContrato(),
                contrato.getValorTotal().toString(),
                contrato.getStatus(),
                Utils.converter(contrato.getDataInicial()),
                Utils.converter(contrato.getDataFinal()),
                contrato.getVigencia(),
                ProcessoDTO.from(contrato.getProcesso()),
                Objects.isNull(contrato.getContratoReferencia()) ? null : contrato.getContratoReferencia().getId(),
                contrato.getFornecedores()
                        .stream()
                        .map(FornecedorDTO::from)
                        .toList(),
                contrato.getItens()
                        .stream()
                        .map(ItemDTO::from)
                        .toList()
        );
    }
}
