package br.ufs.sicad.api.dtos;

import java.util.List;

public record DashboardContratosVencendoDTO(
        List<MesVencimentoDTO> meses
) {
    
    public static DashboardContratosVencendoDTO of(List<MesVencimentoDTO> meses) {
        return new DashboardContratosVencendoDTO(meses);
    }
    
    public record MesVencimentoDTO(
            String mes,
            Long quantidade
    ) {
        public static MesVencimentoDTO of(String mes, Long quantidade) {
            return new MesVencimentoDTO(mes, quantidade);
        }
    }
}
