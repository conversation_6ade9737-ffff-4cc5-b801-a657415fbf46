package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.enums.StatusProcesso;

public record DashboardProcessosPorStatusDTO(
        Long totalProcessos,
        Long processosAtivos,
        Long processosArquivados,
        Long processosConcluidos,
        Long processosPendentes
) {
    
    public static DashboardProcessosPorStatusDTO of(Long total, Long ativos, Long arquivados, Long concluidos, Long pendentes) {
        return new DashboardProcessosPorStatusDTO(total, ativos, arquivados, concluidos, pendentes);
    }
}
