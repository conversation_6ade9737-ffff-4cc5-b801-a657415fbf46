package br.ufs.sicad.api.dtos;

import java.util.List;

public record DashboardProcessosPorTipoDTO(
        List<TipoProcessoDTO> tipos
) {
    
    public static DashboardProcessosPorTipoDTO of(List<TipoProcessoDTO> tipos) {
        return new DashboardProcessosPorTipoDTO(tipos);
    }
    
    public record TipoProcessoDTO(
            String tipo,
            Long quantidade
    ) {
        public static TipoProcessoDTO of(String tipo, Long quantidade) {
            return new TipoProcessoDTO(tipo, quantidade);
        }
    }
}
