package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Documento;
import br.ufs.sicad.domain.enums.Status;

import java.time.LocalDateTime;

public record DocumentoDTO(
        Long id,
        String nomeArquivoOriginal,
        String nomeArquivoUnico,
        String tipoArquivo,
        String url,
        String caminhoArquivo,
        LocalDateTime dataUpload,
        Status status
) {
    public static DocumentoDTO from(Documento documento) {
        return new DocumentoDTO(
                documento.getId(),
                documento.getNomeArquivoOriginal(),
                documento.getNomeArquivoUnico(),
                documento.getTipoArquivo(),
                documento.getUrl(),
                documento.getCaminhoArquivo(),
                documento.getDataUpload(),
                documento.getStatus()
        );
    }
}