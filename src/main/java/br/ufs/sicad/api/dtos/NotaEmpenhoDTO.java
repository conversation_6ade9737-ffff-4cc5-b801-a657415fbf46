package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.Status;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

public record NotaEmpenhoDTO(
        Long id,
        String numero,
        String ano,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        BigDecimal valorTotal,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoEntrega,
        String numContrato,
        String numProcesso,
        Status status
) {
    public static NotaEmpenhoDTO from(NotaEmpenho notaEmpenho) {
        return new NotaEmpenhoDTO(
                notaEmpenho.getId(),
                notaEmpenho.getNumero(),
                notaEmpenho.getAno(),
                notaEmpenho.getDataEmissao(),
                notaEmpenho.getValorTotal(),
                notaEmpenho.getPrazoEntrega(),
                notaEmpenho.getContrato().getNumContrato(),
                notaEmpenho.getProcesso().getNumeroSei(),
                notaEmpenho.getStatus()
        );
    }
}
