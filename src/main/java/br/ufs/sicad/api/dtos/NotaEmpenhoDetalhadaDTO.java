package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.Status;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public record NotaEmpenhoDetalhadaDTO(
        Long id,
        String numero,
        String ano,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        BigDecimal valorTotal,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoEntrega,
        Status status,
        FornecedorDTO fornecedor,
        ContratoDTO contrato,
        ProcessoDTO processo,
        List<NotaEmpenhoItemDTO> itens
) {
    public static NotaEmpenhoDetalhadaDTO from(NotaEmpenho notaEmpenho) {
        return new NotaEmpenhoDetalhadaDTO(
                notaEmpenho.getId(),
                notaEmpenho.getNumero(),
                notaEmpenho.getAno(),
                notaEmpenho.getDataEmissao(),
                notaEmpenho.getValorTotal(),
                notaEmpenho.getPrazoEntrega(),
                notaEmpenho.getStatus(),
                FornecedorDTO.from(notaEmpenho.getFornecedor()),
                notaEmpenho.getContrato() != null ? ContratoDTO.from(notaEmpenho.getContrato()) : null,
                notaEmpenho.getProcesso() != null ? ProcessoDTO.from(notaEmpenho.getProcesso()) : null,
                notaEmpenho.getItens().stream()
                        .map(NotaEmpenhoItemDTO::from)
                        .toList()
        );
    }
}
