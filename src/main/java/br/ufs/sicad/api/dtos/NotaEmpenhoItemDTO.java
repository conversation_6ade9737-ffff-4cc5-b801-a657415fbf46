package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenhoItem;

public record NotaEmpenhoItemDTO(
        ItemDTO item,
        Integer quantidade
) {
    public static NotaEmpenhoItemDTO from(NotaEmpenhoItem notaEmpenhoItem) {
        return new NotaEmpenhoItemDTO(
                ItemDTO.from(notaEmpenhoItem.getItem()),
                notaEmpenhoItem.getQuantidade()
        );
    }
}
