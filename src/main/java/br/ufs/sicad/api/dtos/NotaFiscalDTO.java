package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaFiscal;
import br.ufs.sicad.domain.enums.StatusPagamento;

import java.math.BigDecimal;
import java.time.LocalDate;

public record NotaFiscalDTO(
        Long id,
        String numero,
        LocalDate dataEmissao,
        BigDecimal valor,
        StatusPagamento statusPagamento,
        Boolean ativo,
        DocumentoDTO documento
) {
    public static NotaFiscalDTO from(NotaFiscal notaFiscal) {
        return new NotaFiscalDTO(
                notaFiscal.getId(),
                notaFiscal.getNumero(),
                notaFiscal.getDataEmissao(),
                notaFiscal.getValor(),
                notaFiscal.getStatusPagamento(),
                notaFiscal.getAtivo(),
                notaFiscal.getDocumento() != null ? DocumentoDTO.from(notaFiscal.getDocumento()) : null
        );
    }
}
