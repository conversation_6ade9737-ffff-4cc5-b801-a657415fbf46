package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaFiscal;
import br.ufs.sicad.domain.enums.StatusPagamento;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import java.util.stream.Collectors;

public record NotaFiscalDTO(
        Long id,
        String numero,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        BigDecimal valor,
        StatusPagamento statusPagamento,
        Map<Long, Integer> itensEntregues, // Item ID -> Quantidade
        NotaEmpenhoDTO notaDeEmpenho,
        ContratoDTO contrato,
        DocumentoDTO arquivo
) {
    public static NotaFiscalDTO from(NotaFiscal notaFiscal) {
        return new NotaFiscalDTO(
                notaFiscal.getId(),
                notaFiscal.getNumero(),
                notaFiscal.getDataEmissao(),
                notaFiscal.getValor(),
                notaFiscal.getStatusPagamento(),
                notaFiscal.getItensEntregues().entrySet().stream()
                        .collect(Collectors.toMap(
                                entry -> entry.getKey().getId(),
                                Map.Entry::getValue
                        )),
                notaFiscal.getNotaDeEmpenho() != null ? NotaEmpenhoDTO.from(notaFiscal.getNotaDeEmpenho()) : null,
                notaFiscal.getContrato() != null ? ContratoDTO.from(notaFiscal.getContrato()) : null,
                notaFiscal.getArquivo() != null ? DocumentoDTO.from(notaFiscal.getArquivo()) : null
        );
    }
}
