package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Processo;
import java.time.LocalDate;

public record ProcessoDTO(
        Long id,
        String numeroSei,
        String tipo,
        String modalidade,
        String status,
        LocalDate dataAbertura,
        UnidadeOrganizacionalDTO unidadeRequisitante,
        String nomeSolicitanteProcesso
        
) {
    public static ProcessoDTO from(Processo processo) {
        if (processo == null) return null;
        return new ProcessoDTO(
                processo.getId(),
                processo.getNumeroSei(),
                processo.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                processo.getModalidade().name(),
                processo.getStatus().name(),
                processo.getDataAbertura(),
                UnidadeOrganizacionalDTO.from(processo.getUnidadeRequisitante()),
                processo.getNomeSolicitanteProcesso()
        );
    }
}