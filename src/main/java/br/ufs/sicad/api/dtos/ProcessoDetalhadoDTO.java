package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.*;

import java.time.LocalDate;
import java.util.List;

public record ProcessoDetalhadoDTO(
        Long id,
        String numeroSei,
        String tipo,
        String modalidade,
        String status,
        LocalDate dataAbertura,
        String nomeSolicitante,
        UnidadeOrganizacionalDTO unidadeRequisitante,
        UsuarioDTO criador,
        List<String> processosRelacionados,
        // Campos Específicos de ProcessoSPAL
        List<SolicitacaoSpalDTO>solicitacoes,
//        Campos Especificos para processo servico
        List<RegistroDistribuicaoDTO> registroDistribuicao,
//        Campos Especificos para processo consumo
        String statusEntrega,

//        Campos Especificos para outros tipos de processo
        List<ContratoDTO> contratos

) {
    public static ProcessoDetalhadoDTO from(Processo processo) {
        if (processo instanceof ProcessoSpal processoSpal){
            return new ProcessoDetalhadoDTO(
                    processoSpal.getId(),
                    processoSpal.getNumeroSei(),
                    processoSpal.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                    processoSpal.getModalidade().name(),
                    processoSpal.getStatus().name(),
                    processoSpal.getDataAbertura(),
                    processoSpal.getNomeSolicitanteProcesso(),
                    UnidadeOrganizacionalDTO.from(processoSpal.getUnidadeRequisitante()),
                    UsuarioDTO.from(processoSpal.getCriador()),
                    processoSpal.getProcessosRelacionados(),
                    processoSpal.getSolicitacoes().stream().map(SolicitacaoSpalDTO::from).toList(),
                    null,
                    null,
                    null
            );
        }
        if (processo instanceof ProcessoServico processoServico){
            return new ProcessoDetalhadoDTO(
                    processoServico.getId(),
                    processoServico.getNumeroSei(),
                    processoServico.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                    processoServico.getModalidade().name(),
                    processoServico.getStatus().name(),
                    processoServico.getDataAbertura(),
                    processoServico.getNomeSolicitanteProcesso(),
                    UnidadeOrganizacionalDTO.from(processoServico.getUnidadeRequisitante()),
                    UsuarioDTO.from(processoServico.getCriador()),
                    processoServico.getProcessosRelacionados(),
                    null,
                    processoServico.getRegistrosDistribuicao().stream().map(RegistroDistribuicaoDTO::from).toList(),
                    null,
                    processoServico.getContratos()
                            .stream()
                            .map(ContratoDTO::from)
                            .toList()
            );
        }
        if (processo instanceof ProcessoConsumo processoConsumo){
            return new ProcessoDetalhadoDTO(
                    processoConsumo.getId(),
                    processoConsumo.getNumeroSei(),
                    processoConsumo.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                    processoConsumo.getModalidade().name(),
                    processoConsumo.getStatus().name(),
                    processoConsumo.getDataAbertura(),
                    processoConsumo.getNomeSolicitanteProcesso(),
                    UnidadeOrganizacionalDTO.from(processoConsumo.getUnidadeRequisitante()),
                    UsuarioDTO.from(processoConsumo.getCriador()),
                    processoConsumo.getProcessosRelacionados(),
                    null,
                    null,
//                    processoConsumo.getContrato() != null ? ContratoDTO.from(processoConsumo.getContrato()) : null,
                    processoConsumo.getStatusEntrega(),
                    processoConsumo.getContratos()
                            .stream()
                            .map(ContratoDTO::from)
                            .toList()
            );
        }
        if (processo instanceof ProcessoPermanente processoPermanente){
            return new ProcessoDetalhadoDTO(
                    processoPermanente.getId(),
                    processoPermanente.getNumeroSei(),
                    processoPermanente.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                    processoPermanente.getModalidade().name(),
                    processoPermanente.getStatus().name(),
                    processoPermanente.getDataAbertura(),
                    processoPermanente.getNomeSolicitanteProcesso(),
                    UnidadeOrganizacionalDTO.from(processoPermanente.getUnidadeRequisitante()),
                    UsuarioDTO.from(processoPermanente.getCriador()),
                    processoPermanente.getProcessosRelacionados(),
                    null,
                    processoPermanente.getRegistrosDistribuicao().stream().map(RegistroDistribuicaoDTO::from).toList(),
                    null,
                    processoPermanente.getContratos()
                            .stream()
                            .map(ContratoDTO::from)
                            .toList()
            );
        }
        return null;
    }
}