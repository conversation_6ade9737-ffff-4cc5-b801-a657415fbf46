package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.enums.Status;

import java.util.Objects;

public record UnidadeOrganizacionalDTO(
        Long id,
        String nome,
        String sigla,
        Long unidadeSuperior,
        Status status) {

    public static UnidadeOrganizacionalDTO from(UnidadeOrganizacional unidadeOrganizacional) {

        return new UnidadeOrganizacionalDTO(
                unidadeOrganizacional.getId(),
                unidadeOrganizacional.getNome(),
                unidadeOrganizacional.getSigla(),
                Objects.isNull(unidadeOrganizacional.getUnidadeSuperior()) ? null : unidadeOrganizacional.getUnidadeSuperior().getId(),
                unidadeOrganizacional.getStatus());
    }
}