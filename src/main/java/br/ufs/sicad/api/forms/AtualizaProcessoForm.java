package br.ufs.sicad.api.forms;

import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.utils.Utils;

import java.util.List;

public record AtualizaProcessoForm(
        // Campos Comuns
        String tipo,
        String numeroSei,
        String dataAbertura,
        String justificativa,
        String nomeSolicitanteProcesso,
        Long unidadeRequisitanteId,
        List<String> processosRelacionados,

        // Campos Específicos de ProcessoConsumo
        String statusEntrega
) {
    public Processo asProcesso(){
        Processo processo;

        switch (tipo.toUpperCase()) {
            case "SPAL" -> {
                ProcessoSpal spal = new ProcessoSpal();
                preencherCamposComuns(spal);
                processo = spal;
            }
            case "PERMANENTE" -> {
                ProcessoPermanente permanente = new ProcessoPermanente();
                preencherCamposComuns(permanente);
                processo = permanente;
            }
            case "SERVICO" -> {
                ProcessoServico servico = new ProcessoServico();
                preencherCamposComuns(servico);
                processo = servico;
            }
            case "CONSUMO" -> {
                ProcessoConsumo consumo = new ProcessoConsumo();
                preencherCamposComuns(consumo);
                consumo.setStatusEntrega(this.statusEntrega);
                processo = consumo;
            }
            default -> throw new ValidationException("Tipo de processo inválido: " + this.tipo);
        }

        return processo;
    }

    private void preencherCamposComuns(Processo processo) {
        Usuario criador = new Usuario();
        processo.setCriador(criador);

        UnidadeOrganizacional unidadeRequisitante = new UnidadeOrganizacional();
        unidadeRequisitante.setId(this.unidadeRequisitanteId);
        processo.setUnidadeRequisitante(unidadeRequisitante);

        processo.setNumeroSei(this.numeroSei);
        processo.setDataAbertura(Utils.converter(this.dataAbertura));
        processo.setJustificativa(this.justificativa);
        processo.setNomeSolicitanteProcesso(this.nomeSolicitanteProcesso);
        processo.setProcessosRelacionados(this.processosRelacionados);
    }
}