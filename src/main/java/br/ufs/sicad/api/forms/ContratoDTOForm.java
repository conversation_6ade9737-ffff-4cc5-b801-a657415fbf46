package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.domain.enums.TipoContrato;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public record ContratoDTOForm(
        @NotBlank(message = "Número do contrato é obrigatório")
        String numero,

        Long contratoId, //se for ADITIVO precisa colocar o contrato de referência.

        String tipo,

        String objetoContrato,

        StatusContrato status,

        @NotNull(message = "Data inicial é obrigatória")
        String dataInicial,

        String dataFinal,

        List<Long> fornecedorIds,

        List<Long> itemIds,

        Long idProcesso
) {
    public Contrato asContrato() {
        Contrato contrato = new Contrato();
        contrato.setNumContrato(numero);
        contrato.setTipo(TipoContrato.fromString(tipo));
        contrato.setObjetoContrato(objetoContrato);
        contrato.setStatus(status != null ? status : StatusContrato.VIGENTE);
        contrato.setDataInicial(Utils.converter(dataInicial));
        contrato.setDataFinal(Utils.converter(dataFinal));
        return contrato;
    }
} 