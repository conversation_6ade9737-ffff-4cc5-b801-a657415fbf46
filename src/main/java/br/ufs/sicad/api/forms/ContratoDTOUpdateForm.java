package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.utils.Utils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public record ContratoDTOUpdateForm(
        @NotBlank(message = "Número do contrato é obrigatório")
        String numContrato,

        String objetoContrato,

        StatusContrato status,

        @NotNull(message = "Data inicial é obrigatória")
        String dataInicial,

        String dataFinal,

        Long processoId,

        List<Long> fornecedorIds,

        List<Long> itemIds
) {
    public Contrato asContrato() {
        Contrato contrato = new Contrato();
        contrato.setNumContrato(numContrato);
        contrato.setObjetoContrato(objetoContrato);
        contrato.setStatus(status);
        contrato.setDataInicial(Utils.converter(dataInicial));
        contrato.setDataFinal(Utils.converter(dataFinal));
        return contrato;
    }
} 