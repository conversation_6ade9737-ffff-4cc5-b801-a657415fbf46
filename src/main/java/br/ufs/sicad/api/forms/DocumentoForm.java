package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.Documento;
import br.ufs.sicad.domain.enums.Status;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Form para criação de Documento via JSON.
 * 
 * Este form permite criar um documento fornecendo os dados em JSON,
 * incluindo o conteúdo do arquivo em Base64 ou URL externa.
 */
public record DocumentoForm(
        @NotBlank(message = "O nome do arquivo original é obrigatório")
        String nomeArquivoOriginal,
        
        @NotBlank(message = "O tipo do arquivo é obrigatório")
        String tipoArquivo,
        
        @NotBlank(message = "A URL do documento é obrigatória")
        String url,
        
        String caminhoArquivo
) {
    public Documento asDocumento() {
        Documento documento = new Documento();
        documento.setNomeArquivoOriginal(nomeArquivoOriginal);
        documento.setNomeArquivoUnico(UUID.randomUUID().toString() + getExtensao());
        documento.setTipoArquivo(tipoArquivo);
        documento.setUrl(url);
        documento.setCaminhoArquivo(caminhoArquivo != null ? caminhoArquivo : url);
        documento.setDataUpload(LocalDateTime.now());
        documento.setStatus(Status.ATIVO);
        return documento;
    }
    
    private String getExtensao() {
        if (nomeArquivoOriginal != null && nomeArquivoOriginal.contains(".")) {
            return nomeArquivoOriginal.substring(nomeArquivoOriginal.lastIndexOf("."));
        }
        return "";
    }
}
