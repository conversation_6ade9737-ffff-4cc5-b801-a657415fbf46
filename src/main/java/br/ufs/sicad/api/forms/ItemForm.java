package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.Item;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

public record ItemForm(
        Long idProcesso,

        @NotBlank(message = "O nome do item é obrigatório.")
        @Size(max = 255, message = "O nome do item deve ter no máximo 255 caracteres.")
        String nome,

        String especificacao,

        @NotNull(message = "A quantidade total é obrigatória.")
        @Positive(message = "A quantidade total deve ser um número positivo.")
        Integer quantidadeTotal,

        @NotNull(message = "O valor unitário é obrigatório.")
        @Positive(message = "O valor unitário deve ser um número positivo.")
        BigDecimal valorUnitario
) {

    /**     
     * @return Uma nova entidade Item com os dados do formulário.
     */
    public Item asItem() {
        Item item = new Item();
        item.setNome(this.nome);
        item.setEspecificacao(this.especificacao);
        item.setQuantidadeTotal(this.quantidadeTotal);
        item.setValorUnitario(this.valorUnitario);
        return item;
    }
}