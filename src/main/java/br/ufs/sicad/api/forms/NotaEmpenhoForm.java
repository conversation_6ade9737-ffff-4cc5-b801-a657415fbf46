package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import java.util.List;

public record NotaEmpenhoForm(
        @NotBlank(message = "O número é obrigatório")
        @Size(max = 50, message = "O número deve ter no máximo 50 caracteres")
        String numero,

        String ano,
        
        @NotNull(message = "A data de emissão é obrigatória")
        String dataEmissao,
        
        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        String prazoEntrega,

        @NotNull(message = "O fornecedor é obrigatório")
        Long idFornecedor,

        Long idContrato,

        @NotEmpty(message = "A nota de empenho deve ter pelo menos um item")
        @Valid
        List<NotaEmpenhoItemForm> itens
) {
        public NotaEmpenho asNotaEmpenho(){
                NotaEmpenho notaEmpenho = new NotaEmpenho();
                notaEmpenho.setNumero(numero);
                notaEmpenho.setAno(ano);
                notaEmpenho.setDataEmissao(Utils.converter(dataEmissao));
                notaEmpenho.setPrazoEntrega(Utils.converter(prazoEntrega));
                return notaEmpenho;
        }
}
