package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.Item;
import br.ufs.sicad.domain.entidades.NotaEmpenhoItem;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

public record NotaEmpenhoItemForm(
        @NotNull(message = "O ID do item é obrigatório")
        Long itemId,
        
        @NotNull(message = "A quantidade é obrigatória")
        @Positive(message = "A quantidade deve ser positiva")
        Integer quantidade
) {
        public NotaEmpenhoItem asNotaEmpenhoItem(){
                Item item = new Item();
                item.setId(itemId);
                NotaEmpenhoItem notaEmpenhoItem = new NotaEmpenhoItem();
                notaEmpenhoItem.setItem(item);
                notaEmpenhoItem.setQuantidade(quantidade);
                return notaEmpenhoItem;
        }
}
