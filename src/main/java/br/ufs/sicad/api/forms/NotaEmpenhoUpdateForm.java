package br.ufs.sicad.api.forms;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public record NotaEmpenhoUpdateForm(
        @NotBlank(message = "O número é obrigatório")
        String numero,

        String ano,
        
        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        String dataEmissao,
        
        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        @JsonFormat(pattern = "dd/MM/yyyy")
        String prazoEntrega,
        
        Long idFornecedor
) {
}
