package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.enums.StatusPagamento;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Form para atualização de Nota Fiscal.
 *
 * Este form permite atualizar os dados básicos de uma nota fiscal existente.
 */
public record NotaFiscalUpdateForm(
        @NotBlank(message = "O número da nota fiscal é obrigatório")
        String numero,

        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        String dataEmissao,

        @NotNull(message = "O valor é obrigatório")
        @Positive(message = "O valor deve ser positivo")
        BigDecimal valor,

        @NotNull(message = "O status de pagamento é obrigatório")
        StatusPagamento statusPagamento,

        // Mapa de itens entregues: Item ID -> Quantidade
        Map<Long, Integer> itensEntregues
) {
}
