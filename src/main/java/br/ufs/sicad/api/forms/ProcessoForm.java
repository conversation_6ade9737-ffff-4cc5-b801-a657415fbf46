package br.ufs.sicad.api.forms;

import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public record ProcessoForm(
       
        @NotBlank String numeroSei,
        @NotNull String modalidade,
        @NotBlank String tipo,
        @NotNull(message = "A data de abertura é obrigatório.")
        String dataAbertura,
        String justificativa,
        @NotNull Long criadorId,
        @NotNull Long unidadeRequisitanteId,
        List<String> processosRelacionados,
        String nomeSolicitanteProcesso,
        String statusEntrega
) {
    public Processo asProcesso() {
        Processo processo;

        switch (tipo.toUpperCase()) {
            case "SPAL" -> {
                ProcessoSpal spal = new ProcessoSpal();
                preencherCamposComuns(spal);
                processo = spal;
            }
            case "PERMANENTE" -> {
                ProcessoPermanente permanente = new ProcessoPermanente();
                preencherCamposComuns(permanente);
                processo = permanente;
            }
            case "SERVICO" -> {
                ProcessoServico servico = new ProcessoServico();
                preencherCamposComuns(servico);
                processo = servico;
            }
            case "CONSUMO" -> {
                ProcessoConsumo consumo = new ProcessoConsumo();
                preencherCamposComuns(consumo);
                consumo.setStatusEntrega(this.statusEntrega);
                processo = consumo;
            }
            default -> throw new ValidationException("Tipo de processo inválido: " + this.tipo);
        }

        return processo;
    }

    private void preencherCamposComuns(Processo processo) {
        Usuario criador = new Usuario();
        criador.setId(this.criadorId);
        processo.setCriador(criador);

        UnidadeOrganizacional unidadeRequisitante = new UnidadeOrganizacional();
        unidadeRequisitante.setId(this.unidadeRequisitanteId);
        processo.setUnidadeRequisitante(unidadeRequisitante);

        processo.setNumeroSei(this.numeroSei);
        processo.setModalidade(Modalidade.valueOf(this.modalidade.toUpperCase()));
        processo.setDataAbertura(Utils.converter(this.dataAbertura));
        processo.setJustificativa(this.justificativa);
        processo.setNomeSolicitanteProcesso(this.nomeSolicitanteProcesso);
        processo.setProcessosRelacionados(this.processosRelacionados);
    }
}