package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.enums.Modalidade;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

public record ProcessoSpalForm(
        @NotBlank(message = "O número SEI é obrigatório")
        String numeroSei,

        @NotNull(message = "A modalidade é obrigatória")
        Modalidade modalidade,

        @NotNull(message = "A data de abertura é obrigatória")
        LocalDate dataAbertura,

        String justificativa,

        @NotNull(message = "O ID do criador é obrigatório")
        Long criadorId,

        @NotNull(message = "O ID da unidade requisitante é obrigatório")
        Long unidadeRequisitanteId
) {}