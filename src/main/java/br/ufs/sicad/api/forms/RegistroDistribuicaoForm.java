package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.RegistroDistribuicao;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.Set;

public record RegistroDistribuicaoForm(
        @NotNull(message = "A data de distribuição é obrigatória.")
        LocalDate dataDistribuicao,

        @NotNull(message = "A quantidade distribuída é obrigatória.")
        @Positive(message = "A quantidade deve ser maior que zero.")
        Integer quantidadeDistribuida,

        @NotNull(message = "A unidade de destino é obrigatória.")
        Long unidadeDestinoId,

        @NotNull(message = "O item é obrigatório.")
        Long itemId,

        Set<Long> patrimonios
) {
    public RegistroDistribuicao asRegistroDistribuicao() {
        RegistroDistribuicao registro = new RegistroDistribuicao();
        registro.setDataDistribuicao(this.dataDistribuicao);
        registro.setQuantidadeDistribuida(this.quantidadeDistribuida);
        if (this.patrimonios != null) {
            registro.setPatrimonios(this.patrimonios);
        }
        return registro;
    }
}