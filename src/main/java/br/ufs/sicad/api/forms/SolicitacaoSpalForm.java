package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public record SolicitacaoSpalForm(
        @NotNull String numeroSolicitacao,
        @NotNull String dataSolicitacao,
        Integer numeroGlpi,
        @NotBlank String justificativa,
        @NotEmpty List<String> patrimonios,

        // Campos para requerente interno ou externo
        Long requerenteInternoId,
        String requerenteExternoNome,
        String requerenteExternoDoc,
        String requerenteExternoUnidade
) {

    public SolicitacaoSpal asSolicitacao(){
        SolicitacaoSpal solicitacao = new SolicitacaoSpal();
        solicitacao.setNumeroSolicitacao(this.numeroSolicitacao);
        solicitacao.setDataSolicitacao(Utils.converter(this.dataSolicitacao));
        solicitacao.setNumeroGlpi(this.numeroGlpi);
        solicitacao.setJustificativa(this.justificativa);
        solicitacao.setPatrimonios(this.patrimonios);
        solicitacao.setRequerenteExternoNome(this.requerenteExternoNome);
        solicitacao.setRequerenteExternoDoc(this.requerenteExternoDoc);
        solicitacao.setRequerenteExternoUnidade(this.requerenteExternoUnidade);
        return solicitacao;
    }

    @AssertTrue(message = "Deve ser fornecido um requerente interno (requerenteUsuarioId) ou um externo (requerenteExternoNome), mas não ambos.")
    private boolean isRequerenteValido() {
        boolean internoPresente = requerenteInternoId != null;
        boolean externoPresente = requerenteExternoNome != null && !requerenteExternoNome.isBlank();
        return internoPresente ^ externoPresente; // XOR: um ou outro.
    }
}