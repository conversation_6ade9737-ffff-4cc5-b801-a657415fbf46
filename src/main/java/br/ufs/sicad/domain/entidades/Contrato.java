package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.domain.enums.TipoContrato;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Entidade que representa um contrato no sistema SICAD.
 *
 * <p>Um contrato é um documento legal que estabelece os termos e condições
 * para a prestação de serviços ou fornecimento de produtos. Esta entidade
 * armazena informações como:</p>
 * <ul>
 *   <li>Número e objeto do contrato</li>
 *   <li>Valor total e período de vigência</li>
 *   <li>Status atual do contrato</li>
 *   <li>Fornecedores e itens associados</li>
 *   <li>Aditivos contratuais</li>
 *   <li>Processo relacionado</li>
 * </ul>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "contrato")
@Entity
public class Contrato {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TipoContrato tipo = TipoContrato.CONTRATO;

    @Column(name = "num_contrato", nullable = false, unique = true, length = 100)
    private String numContrato;

    @Column(name = "objeto_contrato", columnDefinition = "text")
    private String objetoContrato;

    @Column(name = "valor_total", nullable = false, precision = 18, scale = 2)
    private BigDecimal valorTotal = new BigDecimal("0");

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 50)
    private StatusContrato status = StatusContrato.VIGENTE;

    @Column(name = "data_inicial", nullable = false)
    private LocalDate dataInicial;

    @Column(name = "data_final")
    private LocalDate dataFinal;

    @Column
    private Integer vigencia; // Vigência em meses

    @ManyToOne
    @JoinColumn(name = "contrato_referencia_id")
    private Contrato contratoReferencia;

    @ManyToMany(cascade = CascadeType.ALL)
    @JoinTable(
        name = "contrato_fornecedor",
        joinColumns = @JoinColumn(name = "contrato_id"),
        inverseJoinColumns = @JoinColumn(name = "fornecedor_id")
    )
    private List<Fornecedor> fornecedores = new ArrayList<>();

    @ManyToMany
    @JoinTable(
        name = "contrato_item",
        joinColumns = @JoinColumn(name = "contrato_id"),
        inverseJoinColumns = @JoinColumn(name = "item_id")
    )
    private List<Item> itens = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processo_id", nullable = false)
    private Processo processo;

    public void setDataFinal(LocalDate dataFinal) {
        this.dataFinal = dataFinal;
        this.vigencia = Objects.isNull(dataFinal) ? null : Math.toIntExact(ChronoUnit.MONTHS.between(dataInicial, dataFinal));
    }

    public void calcularValorTotal() {
        this.valorTotal = itens.stream()
                .map(Item::getValorUnitario)
                .toList()
                .stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}