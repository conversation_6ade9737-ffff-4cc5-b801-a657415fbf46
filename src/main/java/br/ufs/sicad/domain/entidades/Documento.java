package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.Status;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

@Table(name = "documento")
@Getter
@Setter
@Entity
public class Documento {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nome_arquivo_original", nullable = false)
    private String nomeArquivoOriginal;

    @Column(name = "nome_arquivo_unico", nullable = false, unique = true)
    private String nomeArquivoUnico;

    @Column(name = "tipo_arquivo")
    private String tipoArquivo;

    @Column(name = "url", nullable = false, length = 500)
    private String url;

    @Column(name = "caminho_arquivo", nullable = false, length = 500)
    private String caminhoArquivo;

    @Column(name = "data_upload", nullable = false)
    private LocalDateTime dataUpload;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status = Status.ATIVO;

    public void inativar() {
        this.status = Status.INATIVO;
    }
}