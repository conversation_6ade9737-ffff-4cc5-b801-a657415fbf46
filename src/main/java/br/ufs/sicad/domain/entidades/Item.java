package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.config.ValidationException; 
import br.ufs.sicad.domain.enums.StatusItem;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "item")
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "nome", nullable = false, length = 255)
    private String nome;

    @NotNull
    @Column(name = "valor_unitario", nullable = false, precision = 19, scale = 2)
    private BigDecimal valorUnitario;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    private StatusItem status = StatusItem.ATIVO;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processo_id", nullable = false)
    private Processo processo;

    @Column(name = "especificacao", columnDefinition = "text")
    private String especificacao;

    @NotNull
    @Column(name = "quantidade_total", nullable = false)
    private Integer quantidadeTotal = 1;

    @NotNull
    @Column(name = "quantidade_recebida", nullable = false)
    private Integer quantidadeRecebida = 0;
    
    @ElementCollection
    @CollectionTable(name = "item_patrimonios", joinColumns = @JoinColumn(name = "item_id"))
    @Column(name = "patrimonio")
    private Set<Long> patrimonios = new HashSet<>();

    @OneToMany(mappedBy = "item", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<NotaEmpenhoItem> notasEmpenho = new ArrayList<>();

    public void cancelar() {
        if (this.status != StatusItem.ATIVO) {
            throw new ValidationException("Apenas itens com status ATIVO podem ser cancelados.");
        }
        this.status = StatusItem.CANCELADO;
    }

    public void registrarRecebimento(int quantidade, Set<Long> novosPatrimonios) {
        if (this.status == StatusItem.CANCELADO) {
            throw new ValidationException("Não é possível registrar distribuição de um item cancelado.");
        }
        if (quantidade <= 0) {
            throw new ValidationException("A quantidade distribuída deve ser positiva.");
        }
        if (novosPatrimonios != null && !novosPatrimonios.isEmpty()) {
            if (quantidade != novosPatrimonios.size()) {
                throw new ValidationException("A quantidade de patrimônios (" + novosPatrimonios.size() + ") deve ser igual à quantidade distribuída (" + quantidade + ").");
            }
            this.patrimonios.addAll(novosPatrimonios);
        }

        int novaQuantidadeRecebida = this.quantidadeRecebida + quantidade;

        if (novaQuantidadeRecebida > this.quantidadeTotal) {
            throw new ValidationException("A quantidade recebida (" + novaQuantidadeRecebida + ") não pode exceder a quantidade total (" + this.quantidadeTotal + ").");
        }

        this.quantidadeRecebida = novaQuantidadeRecebida;

        if (this.quantidadeRecebida.equals(this.quantidadeTotal)) {
            this.status = StatusItem.RECEBIDO_TOTALMENTE;
        } else {
            this.status = StatusItem.RECEBIDO_PARCIALMENTE;
        }
    }
}