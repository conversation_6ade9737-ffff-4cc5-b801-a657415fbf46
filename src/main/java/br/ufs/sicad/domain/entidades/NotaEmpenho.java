package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.Status;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "nota_empenho", uniqueConstraints = {@UniqueConstraint(columnNames = {"numero", "ano"})})
public class NotaEmpenho {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "O número é obrigatório")
    @Size(max = 50, message = "O número deve ter no máximo 50 caracteres")
    @Column(name = "numero", nullable = false, length = 50)
    private String numero;

    @Size(max = 4, message = "O número deve ter no máximo 4 caracteres")
    @Column(name = "ano", nullable = false, length = 4)
    private String ano;

    @NotNull(message = "A data de emissão é obrigatória")
    @Column(name = "data_emissao", nullable = false)
    private LocalDate dataEmissao;

    @NotNull(message = "O valor total é obrigatório")
    @Positive(message = "O valor total deve ser positivo")
    @Column(name = "valor_total", nullable = false, precision = 15, scale = 2)
    private BigDecimal valorTotal;

    @NotNull(message = "O prazo máximo de entrega é obrigatório")
    @Column(name = "prazo_entrega", nullable = false)
    private LocalDate prazoEntrega;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.ATIVO;

    @NotNull(message = "O fornecedor é obrigatório")
    @ManyToOne
    @JoinColumn(name = "fornecedor_id", nullable = false)
    private Fornecedor fornecedor;

    @ManyToOne
    @JoinColumn(name = "contrato_id", nullable = false)
    private Contrato contrato;

    @ManyToOne
    @JoinColumn(name = "processo_id", nullable = false)
    private Processo processo;

    @OneToMany(mappedBy = "notaEmpenho", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<NotaEmpenhoItem> itens = new ArrayList<>();


    public void inativar() {
        this.status = Status.INATIVO;
    }

    public void ativar() {
        this.status = Status.ATIVO;
    }

    //// Método para calcular valor total baseado nos itens
    public void calcularValorTotal() {
        this.valorTotal = itens.stream()
                .map(i -> i.getItem()
                        .getValorUnitario()
                        .multiply(new BigDecimal(i.getQuantidade())))
                .toList()
                .stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
