package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
public class NotaEmpenhoItem {

    @EmbeddedId
    NotaEmpenhoItemId id;

    @ManyToOne
    @MapsId("notaEmpenhoId")
    @JoinColumn(name = "nota_empenho_id", nullable = false)
    private NotaEmpenho notaEmpenho;

    @ManyToOne(cascade = CascadeType.ALL)
    @MapsId("itemId")
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

    @Column(name = "quantidade", nullable = false)
    private Integer quantidade;
}
