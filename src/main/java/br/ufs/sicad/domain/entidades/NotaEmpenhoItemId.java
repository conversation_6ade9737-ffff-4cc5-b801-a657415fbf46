package br.ufs.sicad.domain.entidades;

import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class NotaEmpenhoItemId  implements Serializable {
    private Long notaEmpenhoId;
    private Long itemId;
}
