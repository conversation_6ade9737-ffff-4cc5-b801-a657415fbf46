package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusPagamento;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "nota_fiscal")
@Entity
public class NotaFiscal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero", nullable = false, length = 100)
    private String numero;

    @Column(name = "data_emissao", nullable = false)
    private LocalDate dataEmissao;

    @Column(name = "valor", nullable = false, precision = 18, scale = 2)
    private BigDecimal valor;

    @Enumerated(EnumType.STRING)
    @Column(name = "status_pagamento", length = 50)
    private StatusPagamento statusPagamento = StatusPagamento.PENDENTE;

    @ElementCollection
    @CollectionTable(name = "nota_fiscal_itens_entregues", joinColumns = @JoinColumn(name = "nota_fiscal_id"))
    @MapKeyJoinColumn(name = "item_id")
    @Column(name = "quantidade_entregue")
    private Map<Item, Integer> itensEntregues = new HashMap<>();

    @ManyToOne
    @JoinColumn(name = "nota_empenho_id", nullable = false)
    private NotaEmpenho notaDeEmpenho;

    @ManyToOne
    @JoinColumn(name = "contrato_id", nullable = false)
    private Contrato contrato;

    @OneToOne
    @JoinColumn(name = "documento_id", unique = true)
    private Documento arquivo;

    // Construtor personalizado
    public NotaFiscal(String numero, LocalDate dataEmissao, BigDecimal valor,
                      NotaEmpenho empenho, Contrato contrato) {
        this.numero = numero;
        this.dataEmissao = dataEmissao;
        this.valor = valor;
        this.notaDeEmpenho = empenho;
        this.contrato = contrato;
        this.statusPagamento = StatusPagamento.PENDENTE;
        this.itensEntregues = new HashMap<>();
    }

    // Métodos de negócio
    public void marcarComoPaga() {
        this.statusPagamento = StatusPagamento.PAGO;
    }

    public void anexarArquivo(Documento doc) {
        this.arquivo = doc;
    }

    // Getters e Setters específicos conforme especificação
    public Long getId() {
        return id;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNumero() {
        return numero;
    }

    public LocalDate getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(LocalDate data) {
        this.dataEmissao = data;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public StatusPagamento getStatusPagamento() {
        return statusPagamento;
    }

    public void setStatusPagamento(StatusPagamento status) {
        this.statusPagamento = status;
    }

    public Map<Item, Integer> getItensEntregues() {
        return itensEntregues;
    }

    public void setItensEntregues(Map<Item, Integer> itens) {
        this.itensEntregues = itens;
    }

    public NotaEmpenho getNotaDeEmpenho() {
        return notaDeEmpenho;
    }

    public void setNotaDeEmpenho(NotaEmpenho empenho) {
        this.notaDeEmpenho = empenho;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Documento getArquivo() {
        return arquivo;
    }

    public void setArquivo(Documento doc) {
        this.arquivo = doc;
    }
}
