package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusPagamento;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "nota_fiscal")
@Entity
public class NotaFiscal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero", nullable = false, length = 100)
    private String numero;

    @Column(name = "data_emissao", nullable = false)
    private LocalDate dataEmissao;

    @Column(name = "valor", nullable = false, precision = 18, scale = 2)
    private BigDecimal valor;

    @Enumerated(EnumType.STRING)
    @Column(name = "status_pagamento", length = 50)
    private StatusPagamento statusPagamento = StatusPagamento.PENDENTE;

    @Column(name = "ativo", nullable = false)
    private Boolean ativo = true;

    @OneToOne
    @JoinColumn(name = "documento_id", nullable = false, unique = true)
    private Documento documento;

}
