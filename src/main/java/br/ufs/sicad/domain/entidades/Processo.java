package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;

import java.util.ArrayList;
import java.time.LocalDate;
import java.util.List;

/**
 * Classe abstrata que representa um processo no sistema SICAD.
 *
 * <p>Um processo é uma sequência de atividades administrativas que visa
 * alcançar um objetivo específico. Esta classe base define os atributos
 * comuns a todos os tipos de processos:</p>
 * <ul>
 *   <li>Número SEI único</li>
 *   <li>Modalidade e status do processo</li>
 *   <li>Dados do solicitante e justificativa</li>
 *   <li>Usuário criador e unidade requisitante</li>
 *   <li>Processos relacionados</li>
 * </ul>
 *
 * <p>Esta classe utiliza herança de tabela única (SINGLE_TABLE) para
 * suportar diferentes tipos de processos como ProcessoSpal, ProcessoServico
 * e ProcessoPermanente.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "processos")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "tipo_processo", discriminatorType = DiscriminatorType.STRING)
public abstract class Processo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero_sei", nullable = false, unique = true, length = 100)
    private String numeroSei;

    @Column(name="tipo", insertable = false, updatable = false) // Coluna readonly, somente para leitura do tipo do processo
    private String tipo;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Modalidade modalidade;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatusProcesso status = StatusProcesso.ATIVO;

    @Column(nullable = false)
    private String nomeSolicitanteProcesso;

    @Column(name = "data_abertura", nullable = false)
    private LocalDate dataAbertura;

    @Column(columnDefinition = "TEXT")
    private String justificativa;

    @ManyToOne
    @JoinColumn(name = "criador_id", nullable = false)
    private Usuario criador;

    @ManyToOne
    @JoinColumn(name = "unidade_requisitante_id", nullable = false)
    private UnidadeOrganizacional unidadeRequisitante;

    @ElementCollection
    @CollectionTable(name = "processosRelacionados", joinColumns = @JoinColumn(name = "processo_id"))
    @Column(name = "processo_relacionado")
    private List<String> processosRelacionados = new ArrayList<>();

    public void ativar(){
        this.setStatus(StatusProcesso.ATIVO);
    }

    public void arquivar(){
        this.setStatus(StatusProcesso.ARQUIVADO);
    };

    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Item> itens = new ArrayList<>();

    public void adicionarItem(Item item) {
         this.itens.add(item);
         item.setProcesso(this);
    }
}
