package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("CONSUMO")
public class ProcessoConsumo extends Processo {
    @Column(name = "status_entrega")
    private String statusEntrega;

    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Contrato> contratos = new ArrayList<>();
}