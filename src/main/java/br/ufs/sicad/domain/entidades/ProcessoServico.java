package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.ArrayList;
import java.util.List;

@Entity
@DiscriminatorValue("SERVICO")
@Getter
@Setter
@NoArgsConstructor
public class ProcessoServico extends Processo {
    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RegistroDistribuicao> registrosDistribuicao = new ArrayList<>();

    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Contrato> contratos = new ArrayList<>();
      
    public void adicionarRegistroDistribuicao(RegistroDistribuicao registro) {
        this.registrosDistribuicao.add(registro);
        registro.setProcesso(this);
    }
}