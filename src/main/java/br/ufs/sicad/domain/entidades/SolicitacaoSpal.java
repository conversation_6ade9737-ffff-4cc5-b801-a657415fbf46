package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusSolicitacaoSpal;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "solicitacoes_spal")
@Getter
@Setter
@NoArgsConstructor
public class SolicitacaoSpal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero_solicitacao", unique = true)
    private String numeroSolicitacao;

    @Column(name = "data_solicitacao", nullable = false)
    private LocalDate dataSolicitacao;

    @Enumerated(EnumType.STRING)
    @Column(name = "status_fluxo", nullable = false)
    private StatusSolicitacaoSpal statusFluxo = StatusSolicitacaoSpal.PENDENTE_ANALISE;

    @Column(name = "numero_glpi")
    private Integer numeroGlpi;

    @Column(columnDefinition = "TEXT")
    private String justificativa;

    @Column(columnDefinition = "TEXT")
    private String analiseTecnica;

    @Column(columnDefinition = "TEXT")
    private String motivoRejeicao;

    // --- Campos do Requerente Híbrido ---
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requerente_usuario_id")
    private Usuario requerenteInterno;

    @Column(name = "requerente_externo_nome")
    private String requerenteExternoNome;

    @Column(name = "requerente_externo_doc")
    private String requerenteExternoDoc;

    @Column(name = "requerente_externo_unidade")
    private String requerenteExternoUnidade;

    // --- Relacionamentos ---
    @ManyToOne
    @JoinColumn(name = "processo_spal_id", nullable = false)
    private ProcessoSpal processoSpal;

//    @OneToOne
//    @JoinColumn(name = "documento_permissao_id", unique = true)
//    private Documento documentoDePermissao;

    @ElementCollection
    @CollectionTable(name = "solicitacao_spal_patrimonios", joinColumns = @JoinColumn(name = "solicitacao_spal_id"))
    @Column(name = "patrimonio", nullable = false)
    private List<String> patrimonios;

    // --- MÉTODOS AUXILIARES ---
    @Transient // Para não ser mapeado pelo JPA
    public boolean isRequerenteInterno() {
        return this.requerenteInterno != null;
    }

    @Transient
    public String getNomeRequerente() {
        return isRequerenteInterno() ? requerenteInterno.getNome() : requerenteExternoNome;
    }

    @Transient
    public String getDocumentoRequerente() {
        return isRequerenteInterno() ? requerenteInterno.getCpf() : requerenteExternoDoc;
    }

    public void arquivar() {
        this.statusFluxo = StatusSolicitacaoSpal.ARQUIVADA;
    }
}