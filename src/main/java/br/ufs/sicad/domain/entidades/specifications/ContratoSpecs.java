package br.ufs.sicad.domain.entidades.specifications;

import br.ufs.sicad.domain.enums.TipoContrato;
import org.springframework.data.jpa.domain.Specification;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Objects;
import java.util.List;

import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.domain.entidades.Contrato;

@AllArgsConstructor
@NoArgsConstructor
public class ContratoSpecs implements Specification<Contrato> {

    public String numContrato;
    public String tipo;
    public LocalDate dataInicial;
    public LocalDate dataFinal;
    public String statusContrato;
    public Long fornecedorId;
    public Long contratoReferencia;

    @Override
    public Predicate toPredicate(Root<Contrato> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.numContrato)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("numero"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.numContrato.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(tipo)) {
            predicates.add(criteriaBuilder.equal(root.get("tipo"), TipoContrato.fromString(tipo)));
        }
        if (StringUtils.isNotEmpty(statusContrato)) {
            predicates.add(criteriaBuilder.equal(root.get("status"), StatusContrato.fromString(statusContrato)));
        }
        if (Objects.nonNull(this.dataInicial)) {
            predicates.add(criteriaBuilder.equal(root.get("dataInicial"), dataInicial));
        }
        if (Objects.nonNull(this.dataFinal)) {
            predicates.add(criteriaBuilder.equal(root.get("dataFinal"), dataFinal));
        }
        if (Objects.nonNull(this.fornecedorId)) {
            predicates.add(criteriaBuilder.equal(root.get("fornecedores").get("id"), fornecedorId));
        }
        if (Objects.nonNull(this.contratoReferencia)) {
            predicates.add(criteriaBuilder.equal(root.get("contratoReferencia").get("id"), contratoReferencia));
        }
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }
}
