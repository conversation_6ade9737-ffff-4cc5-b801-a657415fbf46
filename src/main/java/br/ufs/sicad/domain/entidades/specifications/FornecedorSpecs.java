package br.ufs.sicad.domain.entidades.specifications;

import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import br.ufs.sicad.domain.entidades.Fornecedor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class FornecedorSpecs implements Specification<Fornecedor> {

    public String cnpj;
    public String razaoSocial;

    @Override
    public Predicate toPredicate(Root<Fornecedor> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.cnpj)) {
            predicates.add(criteriaBuilder.like(root.get("cnpj"), "%" + this.cnpj.replaceAll("[^0-9]", "") + "%"));
        }
        if (StringUtils.isNotEmpty(this.razaoSocial)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("razaoSocial"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.razaoSocial.toLowerCase() + "%"));
        }
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }
}
