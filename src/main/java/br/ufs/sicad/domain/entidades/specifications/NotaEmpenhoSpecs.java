package br.ufs.sicad.domain.entidades.specifications;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.enums.Status;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
public class NotaEmpenhoSpecs implements Specification<NotaEmpenho> {
    String numero;
    String numProcesso;
    String numContrato;
    String status;
    LocalDate prazoEntrega;

    @Override
    public Predicate toPredicate(Root<NotaEmpenho> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.numero)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("numero"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.numero.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(this.status)) {
            predicates.add(criteriaBuilder.equal(root.get("status"), Status.fromString(status)));
        }
        if (Objects.nonNull(this.prazoEntrega)) {
            predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("prazoEntrega"), this.prazoEntrega));
        }
        if (StringUtils.isNotEmpty(this.numProcesso)) {
            Join<NotaEmpenho, Processo> processoJoin = root.join("processo", JoinType.INNER);
            Expression<String> processoNumeroExpression = criteriaBuilder.lower(processoJoin.get("numeroSei"));
            predicates.add(criteriaBuilder.like(processoNumeroExpression, "%" + this.numProcesso.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(this.numContrato)) {
            Join<NotaEmpenho, Contrato> contratoJoin = root.join("contrato", JoinType.INNER);
            Expression<String> contratoNumeroExpression = criteriaBuilder.lower(contratoJoin.get("numContrato"));
            predicates.add(criteriaBuilder.like(contratoNumeroExpression, "%" + this.numContrato.toLowerCase() + "%"));
        }
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }
}
