package br.ufs.sicad.domain.entidades.specifications;

import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
public class ProcessoSpecs implements Specification<Processo> {

    public String numeroSei;
    public String tipo;
    public String modalidade;
    public String status;
    public LocalDate dataInicio;
    public LocalDate dataFim;

    @Override
    public Predicate toPredicate(Root<Processo> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        
        if (StringUtils.isNotEmpty(this.numeroSei)) {
            predicates.add(criteriaBuilder.like(root.get("numeroSei"), "%" + this.numeroSei + "%"));
        }
        
        if (StringUtils.isNotEmpty(this.tipo)) {
            Class<? extends Processo> tipoClasse = switch (tipo.toUpperCase()) {
                case "SPAL" -> ProcessoSpal.class;
                case "SERVICO" -> ProcessoServico.class;
                case "CONSUMO" -> ProcessoConsumo.class;
                case "PERMANENTE" -> ProcessoPermanente.class;
                default -> null;
            };
            if (tipoClasse != null) {
                predicates.add(criteriaBuilder.equal(root.type(), tipoClasse));
            }
        }
        
        if (StringUtils.isNotEmpty(this.status)) {
            predicates.add(criteriaBuilder.equal(root.get("status"), StatusProcesso.fromString(status)));
        }
        
        if (StringUtils.isNotEmpty(this.modalidade)) {
            predicates.add(criteriaBuilder.equal(root.get("modalidade"), Modalidade.fromString(modalidade)));
        }
        
        if (Objects.nonNull(this.dataInicio)) {
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("dataAbertura"), dataInicio));
        }
        
        if (Objects.nonNull(this.dataFim)) {
            predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("dataAbertura"), dataFim));
        }
        
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }

}
