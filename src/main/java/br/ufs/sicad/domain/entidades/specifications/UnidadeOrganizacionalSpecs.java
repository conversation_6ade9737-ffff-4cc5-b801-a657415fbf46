package br.ufs.sicad.domain.entidades.specifications;

import br.ufs.sicad.domain.enums.Status;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;

import org.springframework.data.jpa.domain.Specification;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class UnidadeOrganizacionalSpecs implements Specification<UnidadeOrganizacional> {
    public String nome;
    public String sigla;
    public String status;

    @Override
    public Predicate toPredicate(Root<UnidadeOrganizacional> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.nome)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("nome"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.nome.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(this.sigla)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("sigla"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.sigla.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(this.status)) {
            predicates.add(criteriaBuilder.equal(root.get("status"), Status.fromString(status)));
        }
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }
}
