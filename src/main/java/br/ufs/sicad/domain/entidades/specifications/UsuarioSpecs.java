package br.ufs.sicad.domain.entidades.specifications;

import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.domain.enums.Status;

@AllArgsConstructor
@NoArgsConstructor
public class UsuarioSpecs implements Specification<Usuario> {

    public String nome;
    public String matricula;
    public String status;
    public Long perfilId;
    public Long unidadeId;

    @Override
    public Predicate toPredicate(Root<Usuario> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.nome)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("nome"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.nome.toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(this.matricula)) {
            Expression<String> attributeExpression = criteriaBuilder.lower(root.get("matricula"));
            predicates.add(criteriaBuilder.like(attributeExpression, "%" + this.matricula.toLowerCase() + "%"));
        }
        if (Objects.nonNull(this.perfilId)) {
            predicates.add(criteriaBuilder.equal(root.get("perfil").get("id"), perfilId));
        }
        if (StringUtils.isNotEmpty(this.status)) {
            predicates.add(criteriaBuilder.equal(root.get("status"), Status.fromString(status)));
        }
        if (Objects.nonNull(this.unidadeId)) {
            predicates.add(criteriaBuilder.equal(root.get("unidadeOrganizacional").get("id"), unidadeId));
        }
        return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
    }
}
