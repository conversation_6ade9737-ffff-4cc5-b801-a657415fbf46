package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.entidades.Processo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.ArrayList;

@Repository
public interface ContratoRepository extends JpaRepository<Contrato, Long>, JpaSpecificationExecutor<Contrato> {
       ArrayList<Contrato> findByProcesso(Processo processo);
       
       Long countByDataFinalBetween(LocalDate dataInicio, LocalDate dataFim);
       
       Long countByDataFinalAfter(LocalDate data);
}