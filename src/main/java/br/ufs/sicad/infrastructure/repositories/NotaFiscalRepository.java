package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.NotaFiscal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

@Repository
public interface NotaFiscalRepository extends JpaRepository<NotaFiscal, Long> {

    Optional<NotaFiscal> findByNumero(String numero);

    @Query("SELECT nf FROM NotaFiscal nf WHERE " +
           "(:numero IS NULL OR nf.numero LIKE %:numero%) AND " +
           "(:statusPagamento IS NULL OR CAST(nf.statusPagamento AS string) = :statusPagamento) AND " +
           "(:dataEmissao IS NULL OR nf.dataEmissao = :dataEmissao) AND " +
           "(:ativo IS NULL OR nf.ativo = :ativo)")
    Page<NotaFiscal> findByFilters(@Param("numero") String numero,
                                   @Param("statusPagamento") String statusPagamento,
                                   @Param("dataEmissao") LocalDate dataEmissao,
                                   @Param("ativo") Boolean ativo,
                                   Pageable pageable);
}
