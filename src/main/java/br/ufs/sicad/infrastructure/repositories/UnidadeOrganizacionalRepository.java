package br.ufs.sicad.infrastructure.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.enums.Status;

@Repository
public interface UnidadeOrganizacionalRepository extends JpaRepository<UnidadeOrganizacional, Long> , JpaSpecificationExecutor<UnidadeOrganizacional> {
        boolean existsByUnidadeSuperiorId(Long unidadeSuperiorId);
        Page<UnidadeOrganizacional> findByStatus(Status status, Pageable pageable);
}