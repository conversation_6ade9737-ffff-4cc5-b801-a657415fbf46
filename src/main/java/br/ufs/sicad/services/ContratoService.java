package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.specifications.ContratoSpecs;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.domain.enums.TipoContrato;
import br.ufs.sicad.infrastructure.repositories.ContratoRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Serviço responsável pela lógica de negócio relacionada aos contratos no sistema SICAD.
 *
 * <p>Esta classe fornece operações para:</p>
 * <ul>
 *   <li>Criação de novos contratos com associação de fornecedores e itens</li>
 *   <li>Listagem de contratos com filtros avançados</li>
 *   <li>Busca de contratos por ID</li>
 *   <li>Atualização de contratos existentes</li>
 *   <li>Associação de contratos a processos</li>
 * </ul>
 *
 * <p>Todas as operações de escrita são transacionais para garantir a consistência dos dados.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@Service
@RequiredArgsConstructor
public class ContratoService {

    private final ContratoRepository contratoRepository;
    private final ItemService itemService;
    private final ProcessoService processoService;
    private final FornecedorService fornecedorService;

    /**
     * Cria um novo contrato no sistema.
     *
     * <p>Este método cria um contrato associando-o aos fornecedores e itens especificados.
     * Valida a existência de todos os fornecedores e itens antes da criação.</p>
     *
     * @param contrato dados do contrato a ser criado
     * @param fornecedorIds lista de IDs dos fornecedores a serem associados
     * @param itemIds lista de IDs dos itens a serem associados
     * @return o contrato criado e salvo no banco de dados
     * @throws ResourceNotFoundException se algum fornecedor ou item não for encontrado
     */
    @Transactional
    public Contrato criarContrato(Contrato contrato, List<Long> fornecedorIds, List<Long> itemIds, Long processoId, Long contratoReferenciaId) {
        if (ObjectUtils.isEmpty(itemIds)) {
            throw new ValidationException("Contrato deve conter ao menos 1 item.");
        }

        if (Objects.isNull(processoId)) {
            throw new ValidationException("Contrato não pode ser criado sem um processo.");
        }

        if (!ObjectUtils.isEmpty(fornecedorIds)) {
            contrato.setFornecedores(fornecedorIds
                    .stream()
                    .map(fornecedorService::buscarFornecedorPor)
                    .collect(Collectors.toList()));
        }

        if (Objects.nonNull(contratoReferenciaId)){
            if (contrato.getTipo() != TipoContrato.ADITIVO){
                throw new ValidationException("Somente contratos do tipo ADITIVO podem registrar referência a outro contrato.");
            }
            Contrato contratoReferencia = buscarContratoPor(contratoReferenciaId);
            contrato.setContratoReferencia(contratoReferencia);
        }

        if  (contrato.getTipo() == TipoContrato.ADITIVO && Objects.isNull(contratoReferenciaId)){
            throw new ValidationException("Contratos do tipo ADITIVO precisam registrar uma referência a outro contrato.");
        }

        contrato.setItens(itemIds
                .stream()
                .map(itemService::buscarPorId)
                .collect(Collectors.toList()));
        contrato.calcularValorTotal();

        Processo processo = processoService.buscarProcessoPorId(processoId);
        contrato.setProcesso(processo);
        return contratoRepository.save(contrato);
    }

    /**
     * Lista contratos aplicando filtros opcionais.
     *
     * <p>Este método permite buscar contratos com base em diversos critérios como número,
     * status, período de datas, fornecedores e itens associados.</p>
     *
     * @param numContrato filtro opcional pelo número do contrato
     * @param status filtro opcional pelo status do contrato
     * @param dataInicial filtro opcional pela data inicial
     * @param dataFinal filtro opcional pela data final
     * @param fornecedorId filtro opcional pelo ID do fornecedor
     * @param pageable configuração de paginação
     * @return página de contratos que atendem aos critérios
     */
    public Page<Contrato> listarContratos(String numContrato, String tipo, String status, LocalDate dataInicial,
                                         LocalDate dataFinal, Long fornecedorId, Long contratoReferencia, Pageable pageable) {
        Specification<Contrato> contratoSpecs = new ContratoSpecs(numContrato, tipo, dataInicial, dataFinal, status, fornecedorId, contratoReferencia);
        return contratoRepository.findAll(contratoSpecs, pageable);
    }

    /**
     * Busca um contrato pelo seu ID.
     *
     * @param id identificador único do contrato
     * @return o contrato encontrado
     * @throws ResourceNotFoundException se o contrato não for encontrado
     */
    public Contrato buscarContratoPor(Long id) {
        return contratoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Contrato não encontrado. ID: " + id));
    }

    public List<Contrato> listarContratosPorProcesso(Long processoId) {
        Processo processo = processoService.buscarProcessoPorId(processoId);
        return contratoRepository.findByProcesso(processo);
    }

    @Transactional
    public Contrato atualizarContrato(Long id, Contrato contratoAtualizado, Long processoId, List<Long> fornecedorIds, List<Long> itemIds) {
        Contrato contrato = buscarContratoPor(id);
        contrato.setNumContrato(contratoAtualizado.getNumContrato());
        contrato.setObjetoContrato(contratoAtualizado.getObjetoContrato());
        contrato.setValorTotal(contratoAtualizado.getValorTotal());
        contrato.setStatus(contratoAtualizado.getStatus() != null ? contratoAtualizado.getStatus() : StatusContrato.VIGENTE);
        contrato.setDataInicial(contratoAtualizado.getDataInicial());
        contrato.setDataFinal(contratoAtualizado.getDataFinal());

        if (ObjectUtils.isEmpty(itemIds)) {
            throw new ValidationException("Contrato deve conter ao menos 1 item.");
        }

        if (!ObjectUtils.isEmpty(fornecedorIds)) {
            contrato.setFornecedores(fornecedorIds
                    .stream()
                    .map(fornecedorService::buscarFornecedorPor)
                    .collect(Collectors.toList()));
        }

        if (Objects.nonNull(processoId)) {
            Processo processo = processoService.buscarProcessoPorId(processoId);
            contrato.setProcesso(processo);
        }

        contrato.setItens(itemIds
                .stream()
                .map(itemService::buscarPorId)
                .collect(Collectors.toList()));
        contrato.calcularValorTotal();

        return contratoRepository.save(contrato);
    }

    public Long contarContratos(String numContrato, String status, String tipo, 
                               LocalDate dataInicial, LocalDate dataFinal, Long fornecedorId) {
        ContratoSpecs contratoSpecs = new ContratoSpecs(numContrato, tipo, dataInicial, dataFinal, status, fornecedorId, null);
        return contratoRepository.count(contratoSpecs);
    }

    public Long contarAditivos(String numContrato, String status, 
                              LocalDate dataInicial, LocalDate dataFinal, Long fornecedorId) {
        ContratoSpecs contratoSpecs = new ContratoSpecs(numContrato, "ADITIVO", dataInicial, dataFinal, status, fornecedorId, null);
        return contratoRepository.count(contratoSpecs);
    }

    public List<br.ufs.sicad.api.dtos.DashboardContratosVencendoDTO.MesVencimentoDTO> contarContratosVencendoPorMes() {
        LocalDate hoje = LocalDate.now();
        List<br.ufs.sicad.api.dtos.DashboardContratosVencendoDTO.MesVencimentoDTO> meses = new ArrayList<>();
        
        String[] nomesMeses = {
            "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
            "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
        };
        
        // Próximos 12 meses
        for (int i = 0; i < 12; i++) {
            LocalDate inicioMes = hoje.plusMonths(i).withDayOfMonth(1);
            LocalDate fimMes = inicioMes.withDayOfMonth(inicioMes.lengthOfMonth());
            
            Long quantidade = contratoRepository.countByDataFinalBetween(inicioMes, fimMes);
            String nomeMes = nomesMeses[inicioMes.getMonthValue() - 1] + " " + inicioMes.getYear();
            
            meses.add(br.ufs.sicad.api.dtos.DashboardContratosVencendoDTO.MesVencimentoDTO.of(nomeMes, quantidade));
        }
        
        return meses;
    }
} 