package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.StatusItem;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

import java.util.List;


import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ItemService {

    private final ItemRepository itemRepository;
    private final ProcessoService processoService;

    public Item buscarPorId(Long id) {
        return itemRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Item não encontrado com o ID: " + id));
    }

    @Transactional 
    public List<Item> listarItensPorProcesso(Long processoId) {
        Processo processo = processoService.buscarProcessoPorId(processoId);
        return processo.getItens();
    }
    
    @Transactional
    public Item criarItem(Long processoId, Item item) {
        Processo processo = processoService.buscarProcessoPorId(processoId);
        validarProcesso(processo);
        item.setProcesso(processo);
        processo.getItens().add(item);
        return itemRepository.save(item);

    }

    @Transactional
    public Item atualizarItem(Long itemId, Item dadosAtualizados) {
        Item itemExistente = buscarPorId(itemId);
        if (itemExistente.getStatus() != StatusItem.ATIVO) {
            throw new ValidationException("Não é possível editar um item com status: " + itemExistente.getStatus());
        }
        itemExistente.setNome(dadosAtualizados.getNome());
        itemExistente.setEspecificacao(dadosAtualizados.getEspecificacao());
        itemExistente.setQuantidadeTotal(dadosAtualizados.getQuantidadeTotal());
        itemExistente.setValorUnitario(dadosAtualizados.getValorUnitario());
        return itemExistente;
    }

    @Transactional
    public void cancelarItem(Long id) {
        Item item = buscarPorId(id);
        item.cancelar();
    }

    private void validarProcesso(Processo processo) {
        if (!(processo instanceof ProcessoConsumo) && !(processo instanceof ProcessoPermanente) && !(processo instanceof ProcessoServico)) {
            throw new ValidationException("Itens só podem ser adicionados a processos do tipo Consumo, Permanente ou Serviço.");
        }
    }

}