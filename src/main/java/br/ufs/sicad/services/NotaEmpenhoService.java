package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.entidades.specifications.NotaEmpenhoSpecs;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class NotaEmpenhoService {

    private final NotaEmpenhoRepository repository;
    private final FornecedorService fornecedorService;
    private final ContratoService contratoService;
    private final ItemService itemService;

    public Page<NotaEmpenho> listar(String numero, String numProcesso, String numContrato, LocalDate prazoEntrega, String status, Pageable pageable) {
        Specification<NotaEmpenho> notaEmpenhoSpecs = new NotaEmpenhoSpecs(numero, numProcesso, numContrato, status, prazoEntrega);
        return repository.findAll(notaEmpenhoSpecs , pageable);
    }

    public NotaEmpenho buscarPorId(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota de Empenho não encontrada"));
    }


    @Transactional
    public NotaEmpenho criar(NotaEmpenho notaEmpenho, Long idFornecedor, Long idContrato, List<NotaEmpenhoItem> itens) {
        if (Objects.isNull(idFornecedor)) {
            throw new ValidationException("Fornecedor é obrigatório para criar uma nota de empenho");
        }

        if (Objects.isNull(idContrato)) {
            throw new ValidationException("Contrato é obrigatório para criar uma nota de empenho");
        }

        if (ObjectUtils.isEmpty(itens)) {
            throw new IllegalArgumentException("Uma nota de empenho deve ter pelo menos um item");
        }

        // Buscar fornecedor
        Fornecedor fornecedor = fornecedorService.buscarFornecedorPor(idFornecedor);
        notaEmpenho.setFornecedor(fornecedor);

        // Buscar contrato
        Contrato contrato = contratoService.buscarContratoPor(idContrato);
        notaEmpenho.setContrato(contrato);
        notaEmpenho.setProcesso(contrato.getProcesso());

        // Adicionar itens
        for (NotaEmpenhoItem notaEmpenhoItem : itens) {
            Item item = itemService.buscarPorId(notaEmpenhoItem.getItem().getId());
            if (!contrato.getItens().contains(item)){
                throw new ValidationException("Item não pertence ao contrato. Item ID: " + item.getId());
            }
            if (notaEmpenhoItem.getQuantidade() > item.getQuantidadeTotal()){
                    throw new ValidationException(String.format("""
                             A quantidade de itens na nota de empenho não pode exceder a quantidade total de itens do contrato.
                             Item ID: %d  - Quantidade max.: %d""", item.getId(), item.getQuantidadeTotal())
                    );
            }
            notaEmpenhoItem.setId(new NotaEmpenhoItemId(null, item.getId()));
            notaEmpenhoItem.setItem(item);
            notaEmpenhoItem.setNotaEmpenho(notaEmpenho);
            item.getNotasEmpenho().add(notaEmpenhoItem);
            notaEmpenho.getItens().add(notaEmpenhoItem);
        }

        notaEmpenho.calcularValorTotal();

        return repository.save(notaEmpenho);
    }

    @Transactional
    public NotaEmpenho atualizar(Long id, String numero, String ano, LocalDate dataEmissao, LocalDate prazoEntrega, Long idFornecedor){
        NotaEmpenho notaEmpenho = buscarPorId(id);

        if (Objects.nonNull(idFornecedor)) {
            Fornecedor fornecedor = fornecedorService.buscarFornecedorPor(idFornecedor);
            notaEmpenho.setFornecedor(fornecedor);
        }

        notaEmpenho.setNumero(numero);
        notaEmpenho.setAno(ano);
        notaEmpenho.setDataEmissao(dataEmissao);
        notaEmpenho.setPrazoEntrega(prazoEntrega);

        return repository.save(notaEmpenho);
    }

    @Transactional
    public void anularEmpenhoTotal(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.inativar();
        repository.save(notaEmpenho);
    }

    @Transactional
    public void anularEmpenhoParcial(Long id, Long idItem, Integer quantidadeAnulada) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        Optional<NotaEmpenhoItem> optionalItem = notaEmpenho.getItens().stream().filter(i -> i.getItem().getId().equals(idItem)).findFirst();

        if (optionalItem.isEmpty()){
            throw new ValidationException("O item não está registrado na nota de empenho. Item ID: " + idItem);
        }

        NotaEmpenhoItem itemEncontrado = optionalItem.get();

        itemEncontrado.setQuantidade(itemEncontrado.getQuantidade() - quantidadeAnulada);

        if (itemEncontrado.getQuantidade() < 0){
            itemEncontrado.setQuantidade(0);
        }
        repository.save(notaEmpenho);
    }

    @Transactional
    public void inativar(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.inativar();
        repository.save(notaEmpenho);
    }

    @Transactional
    public void reativar(Long id) {
        // Busca incluindo inativas para permitir reativação
        NotaEmpenho notaEmpenho = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota de Empenho não encontrada"));
        notaEmpenho.ativar();
        repository.save(notaEmpenho);
    }
}
