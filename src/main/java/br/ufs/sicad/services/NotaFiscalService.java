package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.StatusPagamento;
import br.ufs.sicad.infrastructure.repositories.DocumentoRepository;
import br.ufs.sicad.infrastructure.repositories.NotaFiscalRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class NotaFiscalService {

    private final NotaFiscalRepository notaFiscalRepository;
    private final DocumentoRepository documentoRepository;
    private final NotaEmpenhoService notaEmpenhoService;
    private final ContratoService contratoService;
    private final ItemService itemService;

    public NotaFiscalService(NotaFiscalRepository notaFiscalRepository,
                            DocumentoRepository documentoRepository,
                            NotaEmpenhoService notaEmpenhoService,
                            ContratoService contratoService,
                            ItemService itemService) {
        this.notaFiscalRepository = notaFiscalRepository;
        this.documentoRepository = documentoRepository;
        this.notaEmpenhoService = notaEmpenhoService;
        this.contratoService = contratoService;
        this.itemService = itemService;
    }

    @Transactional
    public NotaFiscal criarNotaFiscal(NotaFiscal notaFiscal, Long notaEmpenhoId, Long contratoId,
                                     Map<Long, Integer> itensEntregues, Documento documento) {
        // Buscar e associar nota de empenho
        NotaEmpenho notaEmpenho = notaEmpenhoService.buscarPorId(notaEmpenhoId);
        notaFiscal.setNotaDeEmpenho(notaEmpenho);

        // Buscar e associar contrato
        Contrato contrato = contratoService.buscarContratoPor(contratoId);
        notaFiscal.setContrato(contrato);

        // Processar itens entregues
        if (itensEntregues != null && !itensEntregues.isEmpty()) {
            Map<Item, Integer> itensMap = new HashMap<>();
            for (Map.Entry<Long, Integer> entry : itensEntregues.entrySet()) {
                Item item = itemService.buscarPorId(entry.getKey());
                itensMap.put(item, entry.getValue());
            }
            notaFiscal.setItensEntregues(itensMap);
        }

        // Associar documento se fornecido
        if (documento != null) {
            Documento documentoSalvo = documentoRepository.save(documento);
            notaFiscal.anexarArquivo(documentoSalvo);
        }

        return notaFiscalRepository.save(notaFiscal);
    }

    public Page<NotaFiscal> listarNotasFiscais(String numero, String statusPagamento,
                                               LocalDate dataEmissao, Pageable pageable) {

        if (statusPagamento != null && !statusPagamento.trim().isEmpty()) {
            try {
                StatusPagamento.valueOf(statusPagamento.toUpperCase());
            } catch (IllegalArgumentException e) {
                statusPagamento = null;
            }
        }
        return notaFiscalRepository.findByFilters(numero, statusPagamento, dataEmissao, true, pageable);
    }

    public NotaFiscal buscarNotaFiscalPor(Long id) {
        return notaFiscalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota fiscal não encontrada. ID: " + id));
    }

    public List<NotaFiscal> listarTodasNotasFiscais() {
        return notaFiscalRepository.findAll();
    }

    @Transactional
    public NotaFiscal atualizarNotaFiscal(Long id, String numero, LocalDate dataEmissao,
                                         BigDecimal valor, StatusPagamento statusPagamento,
                                         Map<Long, Integer> itensEntregues) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);

        notaFiscal.setNumero(numero);
        notaFiscal.setDataEmissao(dataEmissao);
        notaFiscal.setValor(valor);
        notaFiscal.setStatusPagamento(statusPagamento);

        // Atualizar itens entregues se fornecidos
        if (itensEntregues != null) {
            Map<Item, Integer> itensMap = new HashMap<>();
            for (Map.Entry<Long, Integer> entry : itensEntregues.entrySet()) {
                Item item = itemService.buscarPorId(entry.getKey());
                itensMap.put(item, entry.getValue());
            }
            notaFiscal.setItensEntregues(itensMap);
        }

        return notaFiscalRepository.save(notaFiscal);
    }

    @Transactional
    public NotaFiscal marcarComoPaga(Long id) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);
        notaFiscal.marcarComoPaga();
        return notaFiscalRepository.save(notaFiscal);
    }

    @Transactional
    public NotaFiscal anexarArquivo(Long id, Documento documento) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);
        Documento documentoSalvo = documentoRepository.save(documento);
        notaFiscal.anexarArquivo(documentoSalvo);
        return notaFiscalRepository.save(notaFiscal);
    }

    @Transactional
    public void excluirNotaFiscal(Long id) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);
        if (notaFiscal.getArquivo() != null) {
            notaFiscal.getArquivo().inativar();
        }
        notaFiscalRepository.delete(notaFiscal);
    }
}
