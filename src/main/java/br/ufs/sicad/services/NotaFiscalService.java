package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Documento;
import br.ufs.sicad.domain.entidades.NotaFiscal;
import br.ufs.sicad.domain.enums.StatusPagamento;
import br.ufs.sicad.infrastructure.repositories.DocumentoRepository;
import br.ufs.sicad.infrastructure.repositories.NotaFiscalRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.time.LocalDate;

@Service
public class NotaFiscalService {

    private final NotaFiscalRepository notaFiscalRepository;
    private final DocumentoRepository documentoRepository;
    private final String uploadDir = "files/notas-fiscais/";

    public NotaFiscalService(NotaFiscalRepository notaFiscalRepository,
                            DocumentoRepository documentoRepository) {
        this.notaFiscalRepository = notaFiscalRepository;
        this.documentoRepository = documentoRepository;

        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
        } catch (IOException e) {
            throw new RuntimeException("Erro ao criar diretório de upload", e);
        }
    }

    @Transactional
    public NotaFiscal criarNotaFiscal(NotaFiscal notaFiscal, MultipartFile arquivo) {
        Documento documento = salvarArquivo(arquivo);
        notaFiscal.setDocumento(documento);

        return notaFiscalRepository.save(notaFiscal);
    }

    private Documento salvarArquivo(MultipartFile arquivo) {
        try {
            String nomeOriginal = arquivo.getOriginalFilename();
            String extensao = nomeOriginal.substring(nomeOriginal.lastIndexOf("."));
            String nomeUnico = UUID.randomUUID().toString() + extensao;

            Path caminhoArquivo = Paths.get(uploadDir, nomeUnico);
            Files.copy(arquivo.getInputStream(), caminhoArquivo);

            Documento documento = new Documento();
            documento.setNomeArquivoOriginal(nomeOriginal);
            documento.setNomeArquivoUnico(nomeUnico);
            documento.setTipoArquivo(arquivo.getContentType());
            documento.setCaminhoArquivo(caminhoArquivo.toString());
            documento.setUrl("/api/notas-fiscais/arquivos/" + nomeUnico);
            documento.setDataUpload(LocalDateTime.now());

            return documentoRepository.save(documento);

        } catch (IOException e) {
            throw new RuntimeException("Erro ao salvar arquivo", e);
        }
    }

    public Page<NotaFiscal> listarNotasFiscais(String numero, String statusPagamento,
                                               LocalDate dataEmissao, Boolean ativo, Pageable pageable) {

        if (statusPagamento != null && !statusPagamento.trim().isEmpty()) {
            try {
                StatusPagamento.valueOf(statusPagamento.toUpperCase());
            } catch (IllegalArgumentException e) {
                statusPagamento = null;
            }
        }
        return notaFiscalRepository.findByFilters(numero, statusPagamento, dataEmissao, ativo, pageable);
    }

    public NotaFiscal buscarNotaFiscalPor(Long id) {
        return notaFiscalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota fiscal não encontrada. ID: " + id));
    }

    public List<NotaFiscal> listarTodasNotasFiscais() {
        return notaFiscalRepository.findAll();
    }

    @Transactional
    public NotaFiscal atualizarNotaFiscal(Long id, NotaFiscal notaFiscalAtualizada) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);

        notaFiscal.setNumero(notaFiscalAtualizada.getNumero());
        notaFiscal.setDataEmissao(notaFiscalAtualizada.getDataEmissao());
        notaFiscal.setValor(notaFiscalAtualizada.getValor());
        notaFiscal.setStatusPagamento(notaFiscalAtualizada.getStatusPagamento());
        notaFiscal.setAtivo(notaFiscalAtualizada.getAtivo());

        return notaFiscalRepository.save(notaFiscal);
    }



    public byte[] downloadArquivo(String nomeArquivo) {
        Documento documento = documentoRepository.findByNomeArquivoUnico(nomeArquivo)
                .orElseThrow(() -> new ResourceNotFoundException("Arquivo não encontrado"));

        try {
            Path caminhoArquivo = Paths.get(documento.getCaminhoArquivo());
            return Files.readAllBytes(caminhoArquivo);
        } catch (IOException e) {
            throw new RuntimeException("Erro ao ler arquivo", e);
        }
    }

    @Transactional
    public NotaFiscal inativarNotaFiscal(Long id) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);
        notaFiscal.setAtivo(false);
        return notaFiscalRepository.save(notaFiscal);
    }

    @Transactional
    public NotaFiscal reativarNotaFiscal(Long id) {
        NotaFiscal notaFiscal = buscarNotaFiscalPor(id);
        notaFiscal.setAtivo(true);
        return notaFiscalRepository.save(notaFiscal);
    }
}
