package br.ufs.sicad.services;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.PerfilUsuario;
import br.ufs.sicad.infrastructure.repositories.PerfilUsuarioRepository;

@Service
@RequiredArgsConstructor
public class PerfilService {

    @Autowired
    private final PerfilUsuarioRepository perfilUsuarioRepository;

    public PerfilUsuario buscarPerfilPor(Long id){
        return perfilUsuarioRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Perfil não encontrado: " + id));
    }

    public List<PerfilUsuario> listarPerfis(){
        return perfilUsuarioRepository.findAll();
    }
}
