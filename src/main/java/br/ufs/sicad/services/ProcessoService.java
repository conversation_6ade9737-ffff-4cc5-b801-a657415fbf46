package br.ufs.sicad.services;

import br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO;
import br.ufs.sicad.domain.entidades.specifications.ProcessoSpecs;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import br.ufs.sicad.api.forms.ItemForm;
import br.ufs.sicad.api.forms.RegistroDistribuicaoForm;
import java.time.LocalDate;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import br.ufs.sicad.infrastructure.repositories.ProcessoRepository;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * Serviço responsável pela lógica de negócio relacionada aos processos no sistema SICAD.
 *
 * <p>Esta classe fornece operações para:</p>
 * <ul>
 *   <li>Criação de novos processos com validação de usuários e unidades</li>
 *   <li>Listagem de processos com filtros por tipo, modalidade e status</li>
 *   <li>Busca de processos por ID</li>
 *   <li>Atualização de processos existentes</li>
 *   <li>Arquivamento e ativação de processos</li>
 *   <li>Gerenciamento de processos relacionados</li>
 * </ul>
 *
 * <p>Suporta diferentes tipos de processos como ProcessoSpal, ProcessoServico e ProcessoPermanente.</p>
 *
 * <AUTHOR> SICAD - UFS
 * @version 1.0
 * @since 2024
 */
@Service
public class ProcessoService {

    private final ProcessoRepository processoRepository;
    private final UsuarioRepository usuarioRepository;
    private final UnidadeOrganizacionalRepository unidadeRepository;
    private final ItemRepository itemRepository;

    /**
     * Construtor do serviço de processos.
     *
     * @param processoRepository repositório de processos
     * @param usuarioRepository repositório de usuários
     * @param unidadeRepository repositório de unidades organizacionais
     */
    public ProcessoService(ProcessoRepository processoRepository, UsuarioRepository usuarioRepository, UnidadeOrganizacionalRepository unidadeRepository, ItemRepository itemRepository) {
        this.processoRepository = processoRepository;
        this.usuarioRepository = usuarioRepository;
        this.unidadeRepository = unidadeRepository;
        this.itemRepository = itemRepository;
    }

    /**
     * Lista processos aplicando filtros opcionais.
     *
     * <p>Este método permite buscar processos com base em diversos critérios como número SEI,
     * tipo de processo, modalidade e status.</p>
     *
     * @param numeroSei filtro opcional pelo número SEI do processo
     * @param tipo filtro opcional pelo tipo do processo (SPAL, SERVICO, PERMANENTE)
     * @param modalidade filtro opcional pela modalidade do processo
     * @param status filtro opcional pelo status do processo
     * @param pageable configuração de paginação
     * @return página de processos que atendem aos critérios
     */
    public Page<Processo> listarProcessos(String numeroSei, String tipo, String modalidade, String status, Pageable pageable) {
        Specification<Processo> processoSpecs = new ProcessoSpecs(numeroSei, tipo, modalidade, status, null, null);
        return processoRepository.findAll(processoSpecs, pageable);
    }

    /**
     * Busca um processo pelo seu ID.
     *
     * @param id identificador único do processo
     * @return o processo encontrado
     * @throws EntityNotFoundException se o processo não for encontrado
     */
    public Processo buscarProcessoPorId(Long id) {
        return processoRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Processo com ID " + id + " não encontrado."));
    }

    /**
     * Cria um novo processo no sistema.
     *
     * <p>Este método cria um processo validando a existência do usuário criador e da unidade
     * requisitante. Também permite associar processos relacionados.</p>
     *
     * @param novoProcesso dados do processo a ser criado
     * @return o processo criado e salvo no banco de dados
     * @throws ResourceNotFoundException se o usuário criador ou unidade não forem encontrados
     */
    @Transactional
    public Processo criarProcesso(Processo novoProcesso) {
        Usuario criador = usuarioRepository.findById(novoProcesso.getCriador().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Usuário criador não encontrado."));

        UnidadeOrganizacional unidade = unidadeRepository.findById(novoProcesso.getUnidadeRequisitante().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade requisitante não encontrada."));

        novoProcesso.setCriador(criador);
        novoProcesso.setUnidadeRequisitante(unidade);

        return processoRepository.save(novoProcesso);
    }

    @Transactional
    public Processo atualizarProcesso(Long id, Processo processoAtualizado) {
        Processo processoSalvo = buscarProcessoPorId(id);
        if (!processoAtualizado.getClass().getSimpleName().equalsIgnoreCase(processoSalvo.getClass().getSimpleName())) {
            throw new ValidationException("Tipo de processo incompatível com id.");
        }
        processoSalvo.setNumeroSei(processoAtualizado.getNumeroSei());
        processoSalvo.setDataAbertura(processoAtualizado.getDataAbertura());
        processoSalvo.setJustificativa(processoAtualizado.getJustificativa());
        processoSalvo.setNomeSolicitanteProcesso(processoAtualizado.getNomeSolicitanteProcesso());
        processoSalvo.setUnidadeRequisitante(processoAtualizado.getUnidadeRequisitante());
        processoSalvo.setProcessosRelacionados(processoAtualizado.getProcessosRelacionados());
        return processoRepository.save(processoSalvo);
    }


    @Transactional
    public Processo arquivarProcesso(Long id) {
        Processo processo = buscarProcessoPorId(id);
        processo.arquivar();
        return processoRepository.save(processo);
    }

    @Transactional
    public Processo ativarProcesso(Long id) {
        Processo processo = buscarProcessoPorId(id);
        processo.ativar();
        return processoRepository.save(processo);
    }
    @Transactional
    public Processo adicionarItem(Long processoId, ItemForm itemForm) {
        Processo processo = buscarProcessoPorId(processoId);

        if (processo instanceof ProcessoSpal) {
            throw new ValidationException("Não é possível adicionar itens a um processo do tipo SPAL.");
        }

        Item novoItem = itemForm.asItem();
        processo.adicionarItem(novoItem);
        return processoRepository.save(processo);
    }


    @Transactional
    public Processo adicionarRegistroDistribuicao(Long processoId, RegistroDistribuicaoForm form) {
        Processo processo = buscarProcessoPorId(processoId);

        UnidadeOrganizacional unidadeDestino = unidadeRepository.findById(form.unidadeDestinoId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade de destino não encontrada."));

        Item item = itemRepository.findById(form.itemId())
                .orElseThrow(() -> new ResourceNotFoundException("Item não encontrado."));

        RegistroDistribuicao novoRegistro = new RegistroDistribuicao();
        novoRegistro.setDataDistribuicao(form.dataDistribuicao());
        novoRegistro.setQuantidadeDistribuida(form.quantidadeDistribuida());
        novoRegistro.setUnidadeDestino(unidadeDestino);
        novoRegistro.setItem(item);


        if (processo instanceof ProcessoPermanente permanente) {
            if (form.patrimonios() == null || form.patrimonios().isEmpty()) {
                throw new ValidationException("Para processos do tipo 'Permanente', a lista de patrimónios é obrigatória.");
            }
            novoRegistro.setPatrimonios(form.patrimonios());
            permanente.adicionarRegistroDistribuicao(novoRegistro);

        } else if (processo instanceof ProcessoServico servico) {
            novoRegistro.setPatrimonios(new HashSet<>());
            servico.adicionarRegistroDistribuicao(novoRegistro);

        } else {
            throw new ValidationException("Este tipo de processo não suporta registos de distribuição.");
        }

        return processoRepository.save(processo);
    }

    public Long contarProcessos(String status, String tipo, String modalidade,
                               LocalDate dataInicio, LocalDate dataFim) {
        Specification<Processo> processoSpecs = new ProcessoSpecs(null, tipo, modalidade, status, dataInicio, dataFim);
        return processoRepository.count(processoSpecs);
    }

    public Long[] contarProcessosPorStatus(String tipo, String modalidade,
                                         LocalDate dataInicio, LocalDate dataFim) {
        Long total = contarProcessos(null, tipo, modalidade, dataInicio, dataFim);
        Long ativos = contarProcessos("ATIVO", tipo, modalidade, dataInicio, dataFim);
        Long arquivados = contarProcessos("ARQUIVADO", tipo, modalidade, dataInicio, dataFim);
        Long concluidos = contarProcessos("CONCLUIDO", tipo, modalidade, dataInicio, dataFim);
        Long pendentes = contarProcessos("PENDENTE", tipo, modalidade, dataInicio, dataFim);

        return new Long[]{total, ativos, arquivados, concluidos, pendentes};
    }

    public List<DashboardProcessosPorTipoDTO.TipoProcessoDTO> contarProcessosPorTipo(String modalidade,
                                                                                     LocalDate dataInicio, LocalDate dataFim) {
        List<br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO.TipoProcessoDTO> tipos = new ArrayList<>();

        // SPAL
        Long spal = contarProcessos(null, "SPAL", modalidade, dataInicio, dataFim);
        tipos.add(br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO.TipoProcessoDTO.of("SPAL", spal));

        // SERVICO
        Long servico = contarProcessos(null, "SERVICO", modalidade, dataInicio, dataFim);
        tipos.add(br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO.TipoProcessoDTO.of("SERVICO", servico));

        // CONSUMO
        Long consumo = contarProcessos(null, "CONSUMO", modalidade, dataInicio, dataFim);
        tipos.add(br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO.TipoProcessoDTO.of("CONSUMO", consumo));

        // PERMANENTE
        Long permanente = contarProcessos(null, "PERMANENTE", modalidade, dataInicio, dataFim);
        tipos.add(br.ufs.sicad.api.dtos.DashboardProcessosPorTipoDTO.TipoProcessoDTO.of("PERMANENTE", permanente));

        return tipos;
    }

}