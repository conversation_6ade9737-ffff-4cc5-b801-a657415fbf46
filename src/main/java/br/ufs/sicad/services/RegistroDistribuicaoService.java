package br.ufs.sicad.services;

import br.ufs.sicad.api.forms.RegistroDistribuicaoForm;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Item;
import br.ufs.sicad.domain.entidades.RegistroDistribuicao;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import br.ufs.sicad.infrastructure.repositories.RegistroDistribuicaoRepository;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
public class RegistroDistribuicaoService {

    private final RegistroDistribuicaoRepository registroRepository;
    private final ItemRepository itemRepository;
    private final UnidadeOrganizacionalRepository unidadeRepository; 

    @Transactional
    public RegistroDistribuicao criar(RegistroDistribuicaoForm form) {
        Item item = itemRepository.findById(form.itemId())
                .orElseThrow(() -> new ResourceNotFoundException("Item não encontrado com o ID: " + form.itemId()));

        UnidadeOrganizacional unidade = unidadeRepository.findById(form.unidadeDestinoId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade de destino não encontrada com o ID: " + form.unidadeDestinoId()));

        item.registrarRecebimento(form.quantidadeDistribuida(), form.patrimonios());
        
        RegistroDistribuicao registro = form.asRegistroDistribuicao();
        registro.setItem(item);
        registro.setProcesso(item.getProcesso());
        registro.setUnidadeDestino(unidade);
        
        return registroRepository.save(registro);
    }
}