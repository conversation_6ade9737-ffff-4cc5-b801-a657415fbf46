package br.ufs.sicad.services;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.entidades.specifications.UnidadeOrganizacionalSpecs;
import br.ufs.sicad.domain.enums.Status;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class UnidadeOrganizacionalService {

    private final UnidadeOrganizacionalRepository repository;

    public Page<UnidadeOrganizacional> listarTodos(Pageable pageable) {
        return repository.findByStatus(Status.ATIVO, pageable);
    }

    public Page<UnidadeOrganizacional> listarComFiltros(String nome, String sigla, String status, Pageable pageable) {
        Specification<UnidadeOrganizacional>  unidadeSpecs = new UnidadeOrganizacionalSpecs(nome, sigla, status);
        return repository.findAll(unidadeSpecs, pageable);
    }

    public UnidadeOrganizacional buscarPorId(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada. - Unidade ID: " + id));
    }

    @Transactional
    public UnidadeOrganizacional criar(UnidadeOrganizacional unidade, Long unidadeSuperiorId) {
        if (Objects.nonNull(unidadeSuperiorId)) {
            unidade.setUnidadeSuperior(buscarPorId(unidadeSuperiorId));
        }
        return repository.save(unidade);
    }

    @Transactional
    public UnidadeOrganizacional atualizar(Long id, String novoNome, String novaSigla, Long unidadeSuperiorId) {
        UnidadeOrganizacional unidade = buscarPorId(id);
        unidade.setNome(novoNome);
        unidade.setSigla(novaSigla);
        if (unidadeSuperiorId != null) {
            if (unidadeSuperiorId.equals(id)) {
                throw new ValidationException("Uma unidade não pode ser superior a si mesma.");
            }
            UnidadeOrganizacional unidadeSuperior = buscarPorId(unidadeSuperiorId);
            
            if (Objects.equals(unidadeSuperior.getUnidadeSuperior(), unidade)) {
                throw new ValidationException("Não é possível criar referência circular na hierarquia.");
            }
            unidade.setUnidadeSuperior(unidadeSuperior);
        }
        return repository.save(unidade);
    }

    @Transactional
    public void inativar(Long id) {
        UnidadeOrganizacional unidade = buscarPorId(id);
        if (repository.existsByUnidadeSuperiorId(id)) {
            throw new ValidationException("Não é possível inativar uma unidade que é Superior a outras unidades.");
        }
        unidade.inativar();
        repository.save(unidade);
    }

    @Transactional
    public void ativar(Long id) {
        UnidadeOrganizacional unidade = buscarPorId(id);
        unidade.ativar();
        repository.save(unidade);
    }

    public Long contarUnidades(String nome, String sigla, String status) {
        UnidadeOrganizacionalSpecs unidadeSpecs = new UnidadeOrganizacionalSpecs(nome, sigla, status);
        return repository.count(unidadeSpecs);
    }
}