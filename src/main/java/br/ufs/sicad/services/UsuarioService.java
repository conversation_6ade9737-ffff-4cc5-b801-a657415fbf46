package br.ufs.sicad.services;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import br.ufs.sicad.config.PasswordValidationException;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.domain.entidades.specifications.UsuarioSpecs;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class UsuarioService {
    @Autowired
    private final UsuarioRepository usuarioRepository;
    @Autowired
    private final PerfilService perfilService;
    @Autowired
    private final UnidadeOrganizacionalService unidadeService;

    public Page<Usuario> listarUsuarios(String nome, String matricula, String status,Long unidadeId, Long perfilId, Pageable pageable) {
        Specification<Usuario> usuarioSpecs = new UsuarioSpecs(nome, matricula, status, perfilId, unidadeId);
    return usuarioRepository.findAll(usuarioSpecs, pageable);
}

    public Usuario buscarUsuarioPor(Long UsuarioId) {
        return usuarioRepository.findById(UsuarioId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Usuário ID: " + UsuarioId));
    }

    public Usuario buscarUsuarioPor(String email){
        return usuarioRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Email: " + email));
    }

    public Usuario criarUsuario(Usuario usuario, Long perfilId, Long unidadeId) {
        if (Objects.isNull(perfilId) || Objects.isNull(unidadeId)) {
            throw new ValidationException("Campos Perfil e Unidade são obrigatórios.");
        }
        usuario.setPerfil(perfilService.buscarPerfilPor(perfilId));
        usuario.setUnidadeOrganizacional(unidadeService.buscarPorId(unidadeId));
        return usuarioRepository.save(usuario);
    }

    public Usuario atualizarUsuario(Long id, String matricula, String novoNome,String novoSobrenome, String novoEmail, String novoCpf, String novoTelefone, Long perfilId, Long unidadeId) {
        Usuario usuario = buscarUsuarioPor(id);
        usuario.setMatricula(matricula);
        usuario.setNome(novoNome);
        usuario.setSobrenome(novoSobrenome);
        usuario.setEmail(novoEmail);
        usuario.setCpf(novoCpf);
        usuario.setTelefone(novoTelefone);

        if (Objects.nonNull(unidadeId)) {
            usuario.setUnidadeOrganizacional(unidadeService.buscarPorId(unidadeId));
        }
        if (Objects.nonNull(perfilId)){
            usuario.setPerfil(perfilService.buscarPerfilPor(perfilId));
        }

        return usuarioRepository.save(usuario);
    }

    public void alterarSenha(Long id, String senhaAntiga, String senhaNova){
        Usuario usuario = buscarUsuarioPor(id);
        if (!BCrypt.checkpw(senhaAntiga, usuario.getSenha())){
            throw new PasswordValidationException("Senha atual inválida.");
        }
        usuario.setSenha(new BCryptPasswordEncoder().encode(senhaNova));
        usuarioRepository.save(usuario);
    }

    public void recuperarSenha(Long id){
        Usuario usuario = buscarUsuarioPor(id);
        usuario.setSenha(new BCryptPasswordEncoder().encode(usuario.getCpf().substring(0,6)));
        usuarioRepository.save(usuario);
    }

    public void inativarUsuario(Long id) {
        Usuario usuario = buscarUsuarioPor(id);
        usuario.inativar();
        usuarioRepository.save(usuario);
    }

    public Usuario reativarUsuairo(Long id){
        Usuario usuario = buscarUsuarioPor(id);
        usuario.ativar();
        return usuarioRepository.save(usuario);
    }
}