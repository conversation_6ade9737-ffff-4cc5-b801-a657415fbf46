package br.ufs.sicad.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class Utils {

    public static LocalDate converter(String dataString){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        try {
            return LocalDate.parse(dataString, formatter);
        } catch (DateTimeParseException e) {
            System.err.println("Falha na conversão da data: " + e.getMessage());
        } catch (NullPointerException ex){
            return null;
        }
        return null;
    }

    public static String converter(LocalDate date){
        try{
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return date.format(formatter);
        } catch (DateTimeParseException e) {
            System.err.println("Falha na conversão da data: " + e.getMessage());
        } catch (NullPointerException ex){
            return null;
        }
        return null;
    }
}
